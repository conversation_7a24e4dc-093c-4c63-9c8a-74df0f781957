import fitz  # PyMuPDF（PDF処理ライブラリ）
from PIL import Image
import os
import sys
from utils.path_utils import calculate_job_paths

def pdf_to_jpeg(pdf_path, output_folder, dpi=300):
    """PDFをJPEG画像に変換"""
    try:
        # フォルダ作成
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
            
        # PDFを開く
        pdf_document = fitz.open(pdf_path)
        print(f"PDFには {pdf_document.page_count} ページあります")

        # 各ページを処理
        for page_num in range(pdf_document.page_count):
            # ページを取得
            page = pdf_document[page_num]
            
            # ピクセルマップを取得（高解像度）
            pix = page.get_pixmap(dpi=dpi)
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

            # 出力パス
            output_path = f"{output_folder}/{page_num + 1}.jpg"

            # JPEGで保存（品質85）
            img.save(output_path, "JPEG", quality=85)
            print(f"ページ {page_num + 1} を保存しました: {output_path}")

        print("すべてのページをJPEG画像に変換しました！")
        pdf_document.close()
    except Exception as e:
        print(f"エラーが発生しました: {e}")
        raise

def convert_pdf_to_images_by_job_id(job_id, dpi=150):
    paths = calculate_job_paths(job_id)
    pdf_to_jpeg(paths['pdf_path'], paths['image_dir'], dpi)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python i.py <job_id> [dpi]")
        sys.exit(1)
    
    job_id = sys.argv[1]
    dpi = int(sys.argv[2]) if len(sys.argv) > 2 else 150
    convert_pdf_to_images_by_job_id(job_id, dpi)
