#!/usr/bin/env python3
import cgi
import os
import sys
from utils.path_utils import calculate_job_paths

form = cgi.FieldStorage()
file_id = form.getvalue('id')
content = form.getvalue('content')
job_id = form.getvalue('job_id')

if not file_id or content is None or not job_id:
    print("Error: Invalid request. Missing id, content, or job_id.")
    sys.exit(1)

try:
    paths = calculate_job_paths(job_id)
    file_path = os.path.join(paths['txt_dir'], f"{file_id}.txt")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print("テキストは保存されました。")
except Exception as e:
    print("保存に失敗しました: " + str(e))
