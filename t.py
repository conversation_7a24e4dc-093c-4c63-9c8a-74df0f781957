import os
import pdfplumber
import re
import sys
from utils.path_utils import calculate_job_paths

def extract_text_to_files(pdf_path, output_folder):
    # 出力フォルダを作成（存在しない場合）
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # PDFを開く
    with pdfplumber.open(pdf_path) as pdf:
        for page_num, page in enumerate(pdf.pages, start=1):
            # 各ページのテキストを取得
            text = page.extract_text()

            if text:
                # 英語テキストの場合：2つの単語文字の間にある改行を削除する
                pattern_en = r'(?<=\w)\n(?=\w)'
                text = re.sub(pattern_en, '', text)

                # 日本語テキストの場合：ひらがな、カタカナ、漢字の連続した文字の間にある改行を削除する
                pattern_jp = r'(?<=[\u3040-\u30FF\u4E00-\u9FFF])\n(?=[\u3040-\u30FF\u4E00-\u9FFF])'
                text = re.sub(pattern_jp, '', text)

            # テキストを保存するファイル名を生成
            output_file = os.path.join(output_folder, f"{page_num}.txt")

            # テキストをファイルに書き込む
            with open(output_file, "w", encoding="utf-8") as txt_file:
                txt_file.write(text)

    print(f"PDFのテキストを {output_folder} フォルダに保存しました。")

def extract_text_by_job_id(job_id):
    paths = calculate_job_paths(job_id)
    extract_text_to_files(paths['pdf_path'], paths['txt_dir'])

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python t.py <job_id>")
        sys.exit(1)
    
    job_id = sys.argv[1]
    extract_text_by_job_id(job_id)
