import os
import re
import sys
from utils.path_utils import calculate_job_paths
from utils.file_utils import get_jpg_files_sorted

# 設定システム（環境自動検出）
sys.path.append(os.path.dirname(__file__))
from config import config

def generate_html_files(job_id):
    # ジョブパス取得
    paths = calculate_job_paths(job_id)
    
    image_folder = paths['image_dir']
    text_folder = paths['txt_dir']
    html_folder = paths['html_dir']
    
    # ディレクトリ作成
    os.makedirs(image_folder, exist_ok=True)
    os.makedirs(text_folder, exist_ok=True)
    os.makedirs(html_folder, exist_ok=True)

    # imageフォルダから.jpgファイルを取得し、数値の昇順にソート（utils使用）
    jpg_files_sorted = get_jpg_files_sorted(image_folder)
    
    total_pages = len(jpg_files_sorted)
    for i, jpg_file in enumerate(jpg_files_sorted):
        file_num = os.path.splitext(jpg_file)[0]
        html_filename = f'{file_num}.html'
        html_path = os.path.join(html_folder, html_filename)
        
        # 画像自体はリンクでラップせず、単体のimgタグとして出力
        image_link_html = (
            f'<img id="current-image" src="image/{jpg_file}" alt="{jpg_file}" '
            'style="display:block; width:90%; max-width:90%; margin:0 auto; '
            'box-shadow: 0 4px 8px rgba(0,0,0,0.3);">'
        )
        
        # 左右矢印のナビゲーション（存在する場合のみ）
        if i > 0:
            prev_file_num = os.path.splitext(jpg_files_sorted[i-1])[0]
            left_arrow_html = f'<a href="{prev_file_num}.html" class="nav-arrow left-arrow">◀</a>'
        else:
            left_arrow_html = ""
        if i < total_pages - 1:
            next_file_num = os.path.splitext(jpg_files_sorted[i+1])[0]
            right_arrow_html = f'<a href="{next_file_num}.html" class="nav-arrow right-arrow">▶</a>'
        else:
            right_arrow_html = ""
        arrow_html = left_arrow_html + right_arrow_html

        # 画像とスライダーをラップするコンテナ
        image_container_html = f"""
        <div id="image-container" style="position: relative; display: inline-block;">
            {image_link_html}
            {arrow_html}
            <div id="zoom-slider-container" style="position: absolute; bottom: -30px; left: 50%; transform: translateX(-50%); z-index: 9999; opacity: 0.8; transition: opacity 0.5s;">
                <label style="margin-right: 10px;">－</label>
                <input type="range" id="zoom-slider" min="0.5" max="2" step="0.01" value="1" style="vertical-align: middle;">
                <label style="margin-left: 10px;">＋</label>
            </div>
        </div>
        """
        
        # ナビゲーションリンク
        index_link = '<a href="1.html">表紙</a>'
        if i > 0:
            prev_link = f'<a href="{prev_file_num}.html" rel="prev">&lt; 戻る</a>'
        else:
            prev_link = ''
        if i < total_pages - 1:
            next_link = f'<a href="{next_file_num}.html" rel="next">進む &gt;</a>'
        else:
            next_link = ''
        page_indicator = f'{i+1}/{total_pages}'

        # 目次・サムネイル表示のリンク（行間は元に戻し margin-top:10px）
        nav_links_html = f"""<div style="margin-top: 40px;">
            {index_link} {prev_link} {next_link} <span>{page_indicator}</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        """
        
        # テキストファイルの読み込みと処理
        text_file_name = f'{file_num}.txt'
        text_file_path = os.path.join(text_folder, text_file_name)
        if os.path.exists(text_file_path):
            with open(text_file_path, 'r', encoding='utf-8') as tf:
                text_content = tf.read()
            url_pattern = r'(https?://[^\s]+)'
            text_content = re.sub(url_pattern, r'<a href="\1" target="_blank">\1</a>', text_content)
            text_content_html = text_content.replace('\n', '<br>\n')
        else:
            text_content_html = "テキストがありません。"
        textbox_header = (
            '<span class="textbox-header">\n'
            '<strong>スライド内のプレーンテキスト全文</strong><br>\n'
            '<small>自動抽出のため画像内のテキスト等は表示されない場合があります。</small><br>\n'
            '<br>\n'
            '</span>\n'
        )
        text_box_extra_comment = '<br>\n'
        
        # HTML全体内容
        html_content = f"""<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>{jpg_file}</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="../../../static/css/generated/page.css">
</head>
<body>
  <div style="text-align:center;">
    {image_container_html}
    {nav_links_html}
    <div class="text-box">
      {textbox_header}
      {text_content_html}
      {text_box_extra_comment}
    </div>
    {nav_links_html}
  </div>
  <a id="back-to-top" href="#">↑</a>
  <script src="../../../static/js/generated/page.js"></script>
</body>
</html>
"""
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python h.py <job_id>")
        sys.exit(1)
    
    job_id = sys.argv[1]
    generate_html_files(job_id)
