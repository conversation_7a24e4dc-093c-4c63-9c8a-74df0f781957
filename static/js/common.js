function initializeBackToTop() {
  window.onscroll = function() {
    var backToTop = document.getElementById("back-to-top");
    if (backToTop) {
      if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
        backToTop.style.display = "block";
      } else {
        backToTop.style.display = "none";
      }
    }
  };

  var backToTopBtn = document.getElementById("back-to-top");
  if (backToTopBtn) {
    backToTopBtn.addEventListener("click", function(e) {
      e.preventDefault();
      window.scrollTo({top: 0, behavior: 'smooth'});
    });
  }
}

function initializeGunicornRestart() {
  var restartBtn = document.getElementById("restartBtn");
  if (restartBtn) {
    restartBtn.addEventListener("click", function(e) {
      e.preventDefault();

      // ボタンを無効化して重複クリックを防ぐ
      restartBtn.disabled = true;
      restartBtn.textContent = "再起動中...";

      fetch("/restart", {
        method: "POST",
        headers: {
          'Content-Type': 'application/json',
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.status === "restarted") {
          alert("Gunicorn が再起動されました。");
          // ページをリロードして新しいプロセスに接続
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          alert("再起動エラー: " + (data.error || "不明なエラー"));
        }
      })
      .catch(error => {
        console.error("Restart error:", error);
        alert("エラー: " + error.message);
      })
      .finally(() => {
        // ボタンを再有効化
        restartBtn.disabled = false;
        restartBtn.textContent = "Gunicorn を再起動する";
      });
    });
  }
}

document.addEventListener('DOMContentLoaded', function() {
  initializeBackToTop();
  initializeGunicornRestart();
});
