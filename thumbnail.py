import os
import sys
from utils.path_utils import calculate_job_paths
from utils.file_utils import get_image_files_sorted
from utils.template_utils import load_and_render_template

def generate_thumbnail_html(job_id):
    # ジョブパス取得
    paths = calculate_job_paths(job_id)
    
    image_dir = paths['image_dir']
    output_file = os.path.join(paths['html_dir'], 'thumbnail.html')

    # ディレクトリ作成
    os.makedirs(image_dir, exist_ok=True)

    # 画像ファイル一覧を取得（utils使用）
    files = get_image_files_sorted(image_dir)

    # 画像枚数
    num_images = len(files)

    # 画像タイルのHTMLを作成
    tiles_html = ""
    for f in files:
        name, _ = os.path.splitext(f)
        # リンク先は「ファイル名.html」
        link = f"{name}.html"
        tile = load_and_render_template(
            'thumbnail_tile.html',
            'components',
            link=link,
            filename=f,
            name=name
        )
        tiles_html += tile

    html_content = load_and_render_template(
        'thumbnail_template.html',
        'generators',
        num_images=num_images,
        tiles_html=tiles_html
    )

    # HTMLファイルを保存
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"サムネイルHTMLファイルを {output_file} に保存しました。")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python thumbnail.py <job_id>")
        sys.exit(1)
    
    job_id = sys.argv[1]
    generate_thumbnail_html(job_id)
