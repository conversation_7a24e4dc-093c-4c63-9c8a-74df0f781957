#!/usr/bin/env python3
import os
import sys
import html

# --- ディレクトリ設定 ---
# テキストファイル格納ディレクトリ（絶対パス）
txt_dir = "/var/www/html/zip/html/txt"      # ※書き換えが必要な箇所: ご利用の環境に合わせて変更してください。
# 画像ファイル格納ディレクトリ（絶対パス）
img_dir = "/var/www/html/zip/html/image"    # ※書き換えが必要な箇所: ご利用の環境に合わせて変更してください。
# 生成するHTMLファイルの出力パス（絶対パス）
output_file = "/var/www/html/zip/html/edit.html"  # ※書き換えが必要な箇所: ご利用の環境に合わせて変更してください。

# --- ディレクトリの存在確認 ---
if not os.path.isdir(txt_dir):
    print(f"エラー: テキストディレクトリが存在しません: {txt_dir}")
    sys.exit(1)
if not os.path.isdir(img_dir):
    print(f"エラー: 画像ディレクトリが存在しません: {img_dir}")
    sys.exit(1)

# 出力先ディレクトリの存在確認と作成
output_dir = os.path.dirname(output_file)
if not os.path.exists(output_dir):
    try:
        os.makedirs(output_dir)
    except Exception as e:
        print(f"エラー: 出力ディレクトリの作成に失敗しました: {output_dir} エラー: {e}")
        sys.exit(1)

# --- 画像ファイル一覧の取得 ---
try:
    img_files = os.listdir(img_dir)
except Exception as e:
    print(f"エラー: 画像ディレクトリの読み込みに失敗しました: {img_dir} エラー: {e}")
    sys.exit(1)

rows_html = ""

# --- テキストファイルの一覧取得と処理 ---
try:
    txt_files = sorted(os.listdir(txt_dir))
except Exception as e:
    print(f"エラー: テキストディレクトリの読み込みに失敗しました: {txt_dir} エラー: {e}")
    sys.exit(1)

for txt_file in txt_files:
    if not txt_file.endswith(".txt"):
        continue
    # ファイル名から拡張子を除いた部分（数字）を取得
    file_number = os.path.splitext(txt_file)[0]
    txt_path = os.path.join(txt_dir, txt_file)
    try:
        with open(txt_path, "r", encoding="utf-8") as f:
            text_content = f.read()
    except Exception as e:
        text_content = "読み込みエラー: " + str(e)
    
    text_content_display = html.escape(text_content)
    
    # --- 画像ファイルの検索 (.jpg, .png, .gif) ---
    possible_ext = [".jpg", ".png", ".gif"]
    image_file = ""
    for ext in possible_ext:
        candidate = file_number + ext
        if candidate in img_files:
            image_file = candidate
            break
    # HTML 内での画像参照は、edit.html からの相対パスとする
    img_src = f"image/{image_file}" if image_file else ""
    
    # --- HTMLの行（row）を生成 ---
    row = f'''
    <div class="row">
      <div class="cell cell1">{file_number}</div>
      <div class="cell cell2">'''
    if img_src:
        row += f'<img src="{img_src}" style="max-width:100%; max-height:100%;" />'
    row += f'''</div>
      <div class="cell cell3">
        <button onclick="openEditor('{txt_file}', `{text_content_display}`)">編集</button>
      </div>
      <div class="cell cell4" id="text{file_number}">
        {text_content_display}
        <button id="expand{file_number}" class="expand-btn" onclick="expandText('{file_number}')">＋</button>
        <button id="collapse{file_number}" class="collapse-btn" onclick="collapseText('{file_number}')" style="display:none;">ー</button>
      </div>
    </div>
    '''
    rows_html += row

# --- HTML全体のコンテンツ生成 ---
html_content = f'''<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>テキスト編集ページ</title>
  <style>
    body {{
      margin: 0;
      padding: 0;
    }}
    .header {{
      text-align: center;
      font-size: 24px;
      margin: 20px 0;
    }}
    /* 画面幅100%のうち、左右10%ずつの余白（中央80%のコンテナ） */
    .container {{
      width: 80%;
      margin: 0 auto;
    }}
    .row {{
      display: flex;
      margin-bottom: 10px;
      border: 1px solid #ccc;
    }}
    .cell {{
      border-right: 1px solid #ccc;
      box-sizing: border-box;
    }}
    .cell:last-child {{
      border-right: none;
    }}
    .cell1 {{
      width: 65px;
      padding: 5px;
      text-align: center;
    }}
    .cell2 {{
      width: 627px;
      padding: 5px;
    }}
    .cell3 {{
      width: 70px;
      padding: 5px;
      text-align: center;
    }}
    .cell4 {{
      flex: 1;
      padding: 5px;
      height: 130px;
      overflow: hidden;
      position: relative;
    }}
    .expand-btn, .collapse-btn {{
      position: absolute;
      bottom: 5px;
      right: 5px;
    }}
  </style>
  <script>
    // テキストエリアの展開・縮小機能
    function expandText(id) {{
      var cell = document.getElementById('text' + id);
      cell.style.height = 'auto';
      document.getElementById('expand' + id).style.display = 'none';
      document.getElementById('collapse' + id).style.display = 'inline';
    }}
    function collapseText(id) {{
      var cell = document.getElementById('text' + id);
      cell.style.height = '130px';
      document.getElementById('collapse' + id).style.display = 'none';
      document.getElementById('expand' + id).style.display = 'inline';
    }}
    // 編集ボタン押下時に新規ウィンドウでエディターを表示
    function openEditor(file, text) {{
      var editorWindow = window.open("", "_blank", "width=800,height=600");
      var html = `
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Edit File</title>
      </head>
      <body>
        <h2>Editing File: ${file}</h2>
        <textarea id="editor" style="width:100%; height:80%;">${text}</textarea>
        <br/>
        <button onclick="saveFile()">保存</button>
        <script>
          // ダミーの保存機能：サーバー側の特殊処理は行わず、単にアラートを表示するだけ
          function saveFile() {{
            alert("ダミー保存: テキストは更新されました（実際の保存処理は行っていません）");
          }}
        <\/script>
      </body>
      </html>
      `;
      editorWindow.document.write(html);
    }}
  </script>
</head>
<body>
  <div class="header">テキスト編集ページ</div>
  <div class="container">
    {rows_html}
  </div>
</body>
</html>
'''

# --- HTMLファイルの出力 ---
try:
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(html_content)
    print(f"HTMLファイルを {output_file} に生成しました。")
except Exception as e:
    print(f"エラー: HTMLファイルの書き込みに失敗しました: {output_file} エラー: {e}")
    sys.exit(1)
