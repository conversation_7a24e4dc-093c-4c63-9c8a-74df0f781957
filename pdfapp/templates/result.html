<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <title>PDF→HTML変換</title>
  <style>
    body { font-family: Arial, sans-serif; background-color: #f7f7f7; margin: 0; padding: 0; }
    header { background-color: #234f9e; color: #fff; padding: 10px; }
    header h1 { margin: 0; font-size: 20px; }
    header h1 a { color: #fff; text-decoration: none; }
    header h1 span { font-size: 60%; font-weight: normal; margin-left: 5px; }
    #result-container { max-width: 600px; margin: 50px auto; padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center; }
    h2 { font-size: 24px; margin-bottom: 10px; }
    /* 下に「全○○ページ」を追加 */
    .page-info { font-size: 1em; color: #333; margin-bottom: 20px; }
    a.link { display: inline; font-size: 20px; color: #007acc; text-decoration: none; }
    a.link:hover { text-decoration: underline; }
    .small-link { font-size: 0.8em; margin-top: 20px; }
    /* 1枚目の画像：7%縮小（約511px）で影付き */
    .result-img { width: 511px; max-width: 100%; cursor: pointer; margin-bottom: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
    .info-text { font-size: 0.9em; color: #555; text-align: center; margin-top: 10px; }
    
  </style>
  <script>
    function openPopup(url) {
      window.open(url, 'popup', 'width=1200,height=900');
    }
  </script>
</head>
<body>
  <header>
    <h1>
      <a href="/">PDF→HTML変換</a>
      <span>(プロトタイプ)</span>
    </h1>
  </header>
  <div id="result-container">
    <h2>変換結果</h2>
    <!-- 追加：全○○ページの表示 -->
    <p class="page-info">全 131 ページ</p>
    <!-- １枚目の画像（リンク先変更済み） -->
    <a href="javascript:openPopup('http://*************/zip/html/1.html')">
      <img class="result-img" src="http://*************/zip/html/image/1.jpg" alt="1枚目の画像">
    </a>
    <br>
    <p style="font-size:0.6em; color:#007acc; display:inline;">
      zipファイルをダウンロード： 
      <a class="link" href="http://*************/zip/dl/result.zip" download>result.zip　47.99 MB</a>
    </p>
    <br><br>
    <a class="link" href="javascript:openPopup('http://*************/zip/html/1.html')">HTMLページを表示</a>
      <br><br>  <br><br>
     <a class="link" href="javascript:openPopup('http://*************/zip/html/edit.html')">開発中：テキスト編集画面</a>
  </div>
  <br>   
   <p class="info-text">※現在変換処理の途中で最終結果画面が表示される場合があります。<br>その場合は、数値や画像が正しく反映されるまでF5キーを押して更新願います。（原因調査中）</p>
  <p class="small-link" style="text-align:center;"><a href="/">TOPへ戻る</a></p>
</body>
</html>