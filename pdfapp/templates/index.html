<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <title>PDF→HTML変換</title>
  <style>
    body { font-family: Arial, sans-serif; background-color: #f7f7f7; margin: 0; padding: 0; }
    header { background-color: #234f9e; color: #fff; padding: 10px; }
    header h1 { margin: 0; font-size: 20px; }
    header h1 a { color: #fff; text-decoration: none; }
    header h1 span { font-size: 60%; font-weight: normal; margin-left: 5px; }
    /* 白いアップロードボックス */
       /* #upload-container { max-width: 600px; margin: 50px auto; padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        #upload-container { max-width: 80%; margin: 250px auto; padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
    /* 薄水色背景の水色枠 */
    #drop-area { 
      border: 2px dashed #00bfff; 
      border-radius: 20px; 
      padding: 40px 20px; 
      text-align: center; 
      color: #234f9e; 
      font-size: 130%; 
      background-color: #f0fbff; 
      transition: background-color 0.3s ease, border-color 0.3s ease; 
    }
    #drop-area.hover { 
      border-color: #009acd; 
      background-color: #e0f7ff; 
    }
    /* 「ファイルを選択」だけ下線表示し、オンマウスで背景変化 */
    .select-file {
      text-decoration: underline;
      text-decoration-color: #6a81b6;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    .select-file:hover {
      background-color: #00bfff;
    }
    /* 進捗オーバーレイ：40%位置に表示 */
    .progress-overlay { 
      position: fixed; 
      top: 0; left: 0; width: 100%; height: 100%; 
      background: rgba(0,0,0,0.5); 
      display: none; 
      justify-content: flex-start;
      padding-top: 40vh;
      align-items: center; 
      z-index: 1000;
      flex-direction: column;
    }
    /* 進捗テキスト：10%拡大、白色、影付き */
    #progress-text { 
      font-size: 2.2em; 
      color: #ffffff; 
      text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    }
    .spinner {
      border: 8px solid #f3f3f3;
      border-top: 8px solid #3498db;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
      opacity: 0.6; /* 60%透明 */
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .flash { color: red; text-align: center; }
    .info-text { font-size: 0.9em; color: #555; text-align: center; margin-top: 10px; }
  </style>
</head>
<body>
  <header>
    <h1>
      <a href="/">PDF→HTML変換</a>
      <span>(プロトタイプ)</span>
    </h1>
  </header>
  <div id="upload-container">
    <div id="flash-area"></div>
    <form id="upload-form" method="post" enctype="multipart/form-data">
      <div id="drop-area">
        <p>ここにPDFファイルをドロップ<br><br>または　<span class="select-file">ファイルを選択</span></p>
        <input type="file" name="file" id="fileElem" accept="application/pdf" style="display:none;">
      </div>
    </form>
  </div>
  <p class="info-text">水色の枠にPDFファイルをアップロードすると、HTMLファイルが生成されます。</p>
  <p class="info-text">※このページはサーバー上でのHTML変換を検証するために作成した物で、出力結果を含めデザインは仮の物です。</p>
    <p class="info-text">※現在変換処理の途中で最終結果画面が表示される場合があります。<br>その場合は、数値や画像が正しく反映されるまでF5キーを押して更新願います。（原因調査中）</p>
      <p class="info-text">※MacのサファリブラウザはPDFファイルがアップ出来ない場合があります。</p>
  
  <!-- ページ下部にGunicorn再起動ボタンを追加 -->
<button id="restartBtn">Gunicorn を再起動する</button>
<script>
document.getElementById("restartBtn").addEventListener("click", function(){
    fetch("/restart", { method: "POST" })
    .then(response => response.json())
    .then(data => {
        if(data.status === "restarted"){
           alert("Gunicorn が再起動されました。");
        } else {
           alert("再起動エラー: " + data.error);
        }
    })
    .catch(error => {
       alert("エラー: " + error);
    });
});
</script>

  
  <div class="progress-overlay" id="progress">
    <div class="spinner"></div>
    <div id="progress-text">アップロード中…</div>
  </div>
  <script>
    document.querySelector('.select-file').addEventListener('click', function() {
      document.getElementById('fileElem').click();
    });
    
    function uploadFile(file) {
      if (file.type !== 'application/pdf') {
        alert("PDFファイルのみアップロード可能です。PDFファイルをご用意ください。");
        return;
      }
      var formData = new FormData();
      formData.append('file', file);
      var xhr = new XMLHttpRequest();
      xhr.open('POST', '/upload', true);
      xhr.upload.onprogress = function(e) {
        if(e.lengthComputable) {
          var percentComplete = (e.loaded / e.total) * 100;
          document.getElementById('progress-text').innerHTML = 'アップロード中… ' + Math.round(percentComplete) + '% 完了';
        }
      };
      xhr.onreadystatechange = function() {
        if(xhr.readyState === 4) {
          if(xhr.status === 200) {
            pollProgress();
          } else {
            alert("アップロード中にエラーが発生しました。");
            document.getElementById('progress').style.display = 'none';
          }
        }
      };
      document.getElementById('progress').style.display = 'flex';
      xhr.send(formData);
    }
    
    function pollProgress() {
      var interval = setInterval(function(){
        var xhr = new XMLHttpRequest();
        xhr.open('GET', '/progress', true);
        xhr.onload = function() {
          if(xhr.status === 200) {
            var data = JSON.parse(xhr.responseText);
            document.getElementById('progress-text').innerHTML = '処理中: ' + data.progress + '%';
            if(data.progress >= 100) {
              clearInterval(interval);
              window.location.href = "/result";
            }
          }
        };
        xhr.send();
      }, 1000);
    }
    
    document.addEventListener('DOMContentLoaded', function(){
      var dropArea = document.getElementById('drop-area');
      var fileElem = document.getElementById('fileElem');
      
      dropArea.addEventListener('click', function(){
        fileElem.click();
      });
      fileElem.addEventListener('change', function(){
        if(this.files.length > 0){
          uploadFile(this.files[0]);
        }
      });
      dropArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropArea.classList.add('hover');
      });
      dropArea.addEventListener('dragleave', function(e) {
        dropArea.classList.remove('hover');
      });
      dropArea.addEventListener('drop', function(e) {
        e.preventDefault();
        dropArea.classList.remove('hover');
        var files = e.dataTransfer.files;
        if(files.length > 0){
          if (files[0].type !== 'application/pdf') {
            alert("PDFファイルのみアップロード可能です。PDFファイルをご用意ください。");
            return;
          }
          uploadFile(files[0]);
        }
      });
    });
  </script>
</body>
</html>
