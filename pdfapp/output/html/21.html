<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>21.jpg</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    html { scroll-behavior: smooth; }
    a { text-decoration: none; color: #181818; padding: 5px; }
    a:hover { background-color: #d8f6ff; }
    #back-to-top {
      position: fixed; bottom: 30px; right: 30px; width: 50px; height: 50px;
      background-color: rgba(173,216,230,0.7); color: #fff; text-align: center;
      line-height: 50px; border-radius: 50%; font-size: 24px; display: none;
      z-index: 1000; transition: opacity 0.3s; box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    #back-to-top:hover { opacity: 0.9; }
    /* 画像ズーム・ドラッグ用 */
    #current-image {
      transition: transform 0.2s ease;
      backface-visibility: hidden;
      cursor: move;
    }
    /* 矢印ナビゲーション用 */
    .nav-arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      display: none;
      font-size: 2em;
      color: rgba(0, 0, 0, 0.5);
      text-decoration: none;
      user-select: none;
    }
    #image-container:hover .nav-arrow {
      display: block;
    }
    .left-arrow {
      left: 10px;
    }
    .right-arrow {
      right: 10px;
    }
  </style>
</head>
<body style="background-color: #f3f3f3;">
  <div style="text-align:center;">
    
        <div id="image-container" style="position: relative; display: inline-block;">
            <img id="current-image" src="image/21.jpg" alt="21.jpg" style="display:block; width:90%; max-width:90%; margin:0 auto; box-shadow: 0 4px 8px rgba(0,0,0,0.3);">
            <a href="20.html" class="nav-arrow left-arrow">◀</a><a href="22.html" class="nav-arrow right-arrow">▶</a>
            <div id="zoom-slider-container" style="position: absolute; bottom: -30px; left: 50%; transform: translateX(-50%); z-index: 9999; opacity: 0.8; transition: opacity 0.5s;">
                <label style="margin-right: 10px;">－</label>
                <input type="range" id="zoom-slider" min="0.5" max="2" step="0.01" value="1" style="vertical-align: middle;">
                <label style="margin-left: 10px;">＋</label>
            </div>
        </div>
        
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="20.html" rel="prev">&lt; 戻る</a> <a href="22.html" rel="next">進む &gt;</a> <span>21/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
    <div class="text-box" style="background-color: #ffffff; padding: 32px; margin: 50px auto; display:inline-block; text-align:left; max-width:80%; font-size:103%; line-height:1.65;">
      <span style="color: #5a5a5a;">
<strong>スライド内のプレーンテキスト全文</strong><br>
<small>自動抽出のため画像内のテキスト等は表示されない場合があります。</small><br>
<br>
</span>
<!--
**************************************************
****↓↓ テキスト情報 ↓↓***↓↓ここから↓↓****
**************************************************
-->

      Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏetiêu được xác định trước. Agent có thể tương tác với môi trường, thu thập dữ liệu,<br>
phân tích thông tin và tự ra quyết định để đạt được mục tiêu mà không cần sự canthiệp liên tục của con người.<br>
Cách hoạt động của AI AgentQuy trình hoạt động của một AI agent thường bao gồm các bước sau:<br>
- Tiếp nhận mục tiêu: Agent nhận mục tiêu hoặc nhiệm vụ từ người dùnghoặc hệ thống.<br>
- Thu thập dữ liệu: Agent thu thập thông tin từ môi trường thông qua cácgiao diện phần mềm (API, webhook, dữ liệu người dùng nhập vào, v.v.)<br>
hoặc cảm biến.<br>
- Xử lý và phân tích: Agent sử dụng các mô hình AI (như LLM) để phântích dữ liệu, hiểu ngữ cảnh và xác định các hành động phù hợp.<br>
- Ra quyết định: Dựa trên phân tích, agent tự động lựa chọn hành độngtối ưu để tiến gần hơn đến mục tiêu đã đặt ra.<br>
- Thực thi hành động: Agent thực hiện hành động (gửi tin nhắn, gọi API,<br>
cập nhật dữ liệu, v.v.).<br>
- Phản hồi và học hỏi: Agent đánh giá kết quả, nhận phản hồi từ môitrường hoặc người dùng để cải thiện hiệu suất trong tương lai.<br>
- Agent AI nổi bật ở khả năng tự động hóa, ra quyết định độc lập, họchỏi liên tục và có thể phối hợp với các agent khác để giải quyết các tácvụ phức tạp.<br>
1.4 Công nghệ sử dụng1.4.1 Ngôn ngữ lập trìnhNode.js[5]: là một môi trường chạy JavaScript mã nguồn mở, đa nền tảng,<br>
được xây dựng trên engine V8 của Google Chrome. Khác với JavaScript truyềnthống chỉ chạy trên trình duyệt, Node.js cho phép lập trình viên sử dụng JavaScriptđể phát triển các ứng dụng phía máy chủ (backend).<br>
Đặc điểm nổi bật của Node.js:<br>
- Kiến trúc bất đồng bộ (asynchronous) và hướng sự kiện (event-<br>
driven): Node.js xử lý nhiều kết nối đồng thời mà không bị chặn luồng,<br>
lý tưởng cho các ứng dụng thời gian thực, chat, streaming, IoT, v.v..<br>
Sinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 15
      <br>
<!--
**************************************************
****↑↑ テキスト情報 ↑↑***↑↑ここまで↑↑****
**************************************************
-->

    </div>
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="20.html" rel="prev">&lt; 戻る</a> <a href="22.html" rel="next">進む &gt;</a> <span>21/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
  </div>
  <a id="back-to-top" href="#">↑</a>
  <script>
    // キーボードショートカット
    document.addEventListener('keydown', function(event) {
      if (event.key === 'ArrowLeft') {
        var prevLink = document.querySelector('a[rel="prev"]');
        if (prevLink) { window.location.href = prevLink.href; }
      }
      if (event.key === 'ArrowRight') {
        var nextLink = document.querySelector('a[rel="next"]');
        if (nextLink) { window.location.href = nextLink.href; }
      }
    });
    // 上に戻るボタンの表示
    window.onscroll = function() {
      var backToTop = document.getElementById("back-to-top");
      if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
        backToTop.style.display = "block";
      } else {
        backToTop.style.display = "none";
      }
    };
    document.getElementById("back-to-top").addEventListener("click", function(e) {
      e.preventDefault();
      window.scrollTo({top: 0, behavior: 'smooth'});
    });
    // 画像ズーム・ドラッグ処理
    var slider = document.getElementById("zoom-slider");
    var currentImage = document.getElementById("current-image");

    var currentScale = slider ? parseFloat(slider.value) : 1;
    var currentTranslateX = 0;
    var currentTranslateY = 0;

    function updateTransform() {
      currentImage.style.transform = "translate(" + currentTranslateX + "px, " + currentTranslateY + "px) scale(" + currentScale + ")";
    }

    if (slider && currentImage) {
      slider.addEventListener("input", function() {
        currentScale = parseFloat(this.value);
        updateTransform();
      });
    }

    var isDragging = false;
    var dragStartX = 0;
    var dragStartY = 0;

    if (currentImage) {
      currentImage.addEventListener("mousedown", function(e) {
        isDragging = true;
        dragStartX = e.clientX;
        dragStartY = e.clientY;
        e.preventDefault();
      });

      document.addEventListener("mousemove", function(e) {
        if (isDragging) {
          var deltaX = e.clientX - dragStartX;
          var deltaY = e.clientY - dragStartY;
          dragStartX = e.clientX;
          dragStartY = e.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("mouseup", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });

      currentImage.addEventListener("touchstart", function(e) {
        isDragging = true;
        var touch = e.touches[0];
        dragStartX = touch.clientX;
        dragStartY = touch.clientY;
      });

      document.addEventListener("touchmove", function(e) {
        if (isDragging) {
          var touch = e.touches[0];
          var deltaX = touch.clientX - dragStartX;
          var deltaY = touch.clientY - dragStartY;
          dragStartX = touch.clientX;
          dragStartY = touch.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("touchend", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });
    }

    // スライダー自動非表示処理
    var sliderContainer = document.getElementById("zoom-slider-container");
    var imageContainer = document.getElementById("image-container");
    var sliderTimeout;
    function hideSlider() {
      sliderContainer.style.opacity = "0";
    }
    function showSlider() {
      sliderContainer.style.opacity = "0.8";
      clearTimeout(sliderTimeout);
      sliderTimeout = setTimeout(hideSlider, 3000);
    }
    sliderTimeout = setTimeout(hideSlider, 3000);
    imageContainer.addEventListener("mouseover", showSlider);
    imageContainer.addEventListener("mouseleave", function() {
      sliderTimeout = setTimeout(hideSlider, 3000);
    });
  </script>
</body>
</html>
