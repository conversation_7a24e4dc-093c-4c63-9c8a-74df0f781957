<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>57.jpg</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    html { scroll-behavior: smooth; }
    a { text-decoration: none; color: #181818; padding: 5px; }
    a:hover { background-color: #d8f6ff; }
    #back-to-top {
      position: fixed; bottom: 30px; right: 30px; width: 50px; height: 50px;
      background-color: rgba(173,216,230,0.7); color: #fff; text-align: center;
      line-height: 50px; border-radius: 50%; font-size: 24px; display: none;
      z-index: 1000; transition: opacity 0.3s; box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    #back-to-top:hover { opacity: 0.9; }
    /* 画像ズーム・ドラッグ用 */
    #current-image {
      transition: transform 0.2s ease;
      backface-visibility: hidden;
      cursor: move;
    }
    /* 矢印ナビゲーション用 */
    .nav-arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      display: none;
      font-size: 2em;
      color: rgba(0, 0, 0, 0.5);
      text-decoration: none;
      user-select: none;
    }
    #image-container:hover .nav-arrow {
      display: block;
    }
    .left-arrow {
      left: 10px;
    }
    .right-arrow {
      right: 10px;
    }
  </style>
</head>
<body style="background-color: #f3f3f3;">
  <div style="text-align:center;">
    
        <div id="image-container" style="position: relative; display: inline-block;">
            <img id="current-image" src="image/57.jpg" alt="57.jpg" style="display:block; width:90%; max-width:90%; margin:0 auto; box-shadow: 0 4px 8px rgba(0,0,0,0.3);">
            <a href="56.html" class="nav-arrow left-arrow">◀</a><a href="58.html" class="nav-arrow right-arrow">▶</a>
            <div id="zoom-slider-container" style="position: absolute; bottom: -30px; left: 50%; transform: translateX(-50%); z-index: 9999; opacity: 0.8; transition: opacity 0.5s;">
                <label style="margin-right: 10px;">－</label>
                <input type="range" id="zoom-slider" min="0.5" max="2" step="0.01" value="1" style="vertical-align: middle;">
                <label style="margin-left: 10px;">＋</label>
            </div>
        </div>
        
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="56.html" rel="prev">&lt; 戻る</a> <a href="58.html" rel="next">進む &gt;</a> <span>57/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
    <div class="text-box" style="background-color: #ffffff; padding: 32px; margin: 50px auto; display:inline-block; text-align:left; max-width:80%; font-size:103%; line-height:1.65;">
      <span style="color: #5a5a5a;">
<strong>スライド内のプレーンテキスト全文</strong><br>
<small>自動抽出のため画像内のテキスト等は表示されない場合があります。</small><br>
<br>
</span>
<!--
**************************************************
****↓↓ テキスト情報 ↓↓***↓↓ここから↓↓****
**************************************************
-->

      Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏe2.3.2 Cơ sở dữ liệuHeadOfDepartment Doctor Nurse Adminid varchar(30) 00....11 id varchar(30) 0 1 ..1 id varchar(30) 0..1 id varchar(30)<br>
password varchar(30) password varchar(30) password varchar(30) password varchar(30)<br>
fullname varchar(80) fullname varchar(80) fullname varchar(80) fullname varchar(80)<br>
roleID varchar(50) roleID varchar(50) roleID varchar(50) roleID varchar(50)<br>
specialization varchar(50) specialization varchar(50) specialization varchar(50) email varchar(80)<br>
experience int experience int experience int imageUrl varchar(255)<br>
email varchar(80) email varchar(80) email varchar(80) googleId varchar(50)<br>
imageUrl varchar(255) imageUrl varchar(255) imageUrl varchar(255) propicId varchar(30)<br>
googleId varchar(50) googleId varchar(50) googleId varchar(50) dob datepropicId varchar(30) propicId varchar(30) propicId varchar(30) gender varchar(10)<br>
dob date dob date dob date phone varchar(20)<br>
gender varchar(10) gender varchar(10) gender varchar(10) address varchar(100)<br>
phone varchar(20) phone varchar(20) phone varchar(20) city varchar(100)<br>
address varchar(100) address varchar(100) address varchar(100) country varchar(100)<br>
city varchar(100) city varchar(100) city varchar(100) bio varchar(800)<br>
country varchar(100) country varchar(100) country varchar(100) verified booleanbio varchar(800) bio varchar(800) bio varchar(800) active booleanverified boolean verified boolean verified booleanactive boolean active boolean active booleanCommentid varchar(30)<br>
*<br>
Patient Pharmacy Blog userId varchar(30)<br>
00000..........11111 0..1 0..1 *<br>
id varchar(30) id varchar(30) title varchar(100) blogId varchar(100)<br>
fullname varchar(80) location varchar(100) description varchar(1000) content varchar(1000)<br>
email varchar(80) medicines varchar(850) author varchar(100) createdAt datetimephone varchar(20) specialization varchar(100) updatedAt datetimeaddress varchar(100) media varchar(100) reported booleanDoctorScheduledob date slug varchar(100)<br>
*<br>
doctorId varchar(30)<br>
gender varchar(10) likes intavailableSlots varchar(1000) Vitalscity varchar(100) comments varchar(1000)<br>
*<br>
appointmentId varchar(30)<br>
country varchar(100) visibility varchar(10)<br>
*<br>
userId varchar(30)<br>
bio varchar(800) MedicalResultpulse varchar(20)<br>
verified boolean appointmentId varchar(30) *<br>
Appointment bloodPressure varchar(20)<br>
active boolean docResults varchar(1000) 111 11id varchar(30) temperature varchar(20)<br>
testResults varchar(1000) *<br>
userId varchar(30) weight varchar(20)<br>
notes varchar(250) *<br>
Notification doctorId varchar(30) height varchar(20)<br>
* *<br>
userId varchar(30) nurseId varchar(30) generalCondition varchar(100)<br>
message varchar(255) date datetype varchar(50) time timeServiceisRead boolean status varchar(50)<br>
name varchar(30)<br>
relatedId varchar(30) symptoms varchar(1000)<br>
description varchar(200)<br>
diagnosis varchar(1000)<br>
department varchar(100)<br>
medicalHistory varchar(1000)<br>
Prescription price numeric(8,2)<br>
*<br>
appointmentId varchar(30) status varchar(10)<br>
*<br>
doctorId varchar(30) Tests<br>
*<br>
medicinesName varchar(200) appointmentId varchar(30)<br>
*<br>
quantity varchar(20) userId varchar(30)<br>
usage varchar(200) testType varchar(100)<br>
testResults varchar(1000)<br>
Bill<br>
*<br>
appointmentId varchar(30)<br>
userId varchar(30)<br>
*<br>
doctorId varchar(30)<br>
*<br>
pharmacyId varchar(30)<br>
dateIssued datetimepaymentStatus varchar(20)<br>
paymentMethod varchar(20)<br>
totalAmount numeric(8,2)<br>
Hình 2.38 Cơ sở dữ liệuSinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 51
      <br>
<!--
**************************************************
****↑↑ テキスト情報 ↑↑***↑↑ここまで↑↑****
**************************************************
-->

    </div>
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="56.html" rel="prev">&lt; 戻る</a> <a href="58.html" rel="next">進む &gt;</a> <span>57/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
  </div>
  <a id="back-to-top" href="#">↑</a>
  <script>
    // キーボードショートカット
    document.addEventListener('keydown', function(event) {
      if (event.key === 'ArrowLeft') {
        var prevLink = document.querySelector('a[rel="prev"]');
        if (prevLink) { window.location.href = prevLink.href; }
      }
      if (event.key === 'ArrowRight') {
        var nextLink = document.querySelector('a[rel="next"]');
        if (nextLink) { window.location.href = nextLink.href; }
      }
    });
    // 上に戻るボタンの表示
    window.onscroll = function() {
      var backToTop = document.getElementById("back-to-top");
      if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
        backToTop.style.display = "block";
      } else {
        backToTop.style.display = "none";
      }
    };
    document.getElementById("back-to-top").addEventListener("click", function(e) {
      e.preventDefault();
      window.scrollTo({top: 0, behavior: 'smooth'});
    });
    // 画像ズーム・ドラッグ処理
    var slider = document.getElementById("zoom-slider");
    var currentImage = document.getElementById("current-image");

    var currentScale = slider ? parseFloat(slider.value) : 1;
    var currentTranslateX = 0;
    var currentTranslateY = 0;

    function updateTransform() {
      currentImage.style.transform = "translate(" + currentTranslateX + "px, " + currentTranslateY + "px) scale(" + currentScale + ")";
    }

    if (slider && currentImage) {
      slider.addEventListener("input", function() {
        currentScale = parseFloat(this.value);
        updateTransform();
      });
    }

    var isDragging = false;
    var dragStartX = 0;
    var dragStartY = 0;

    if (currentImage) {
      currentImage.addEventListener("mousedown", function(e) {
        isDragging = true;
        dragStartX = e.clientX;
        dragStartY = e.clientY;
        e.preventDefault();
      });

      document.addEventListener("mousemove", function(e) {
        if (isDragging) {
          var deltaX = e.clientX - dragStartX;
          var deltaY = e.clientY - dragStartY;
          dragStartX = e.clientX;
          dragStartY = e.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("mouseup", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });

      currentImage.addEventListener("touchstart", function(e) {
        isDragging = true;
        var touch = e.touches[0];
        dragStartX = touch.clientX;
        dragStartY = touch.clientY;
      });

      document.addEventListener("touchmove", function(e) {
        if (isDragging) {
          var touch = e.touches[0];
          var deltaX = touch.clientX - dragStartX;
          var deltaY = touch.clientY - dragStartY;
          dragStartX = touch.clientX;
          dragStartY = touch.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("touchend", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });
    }

    // スライダー自動非表示処理
    var sliderContainer = document.getElementById("zoom-slider-container");
    var imageContainer = document.getElementById("image-container");
    var sliderTimeout;
    function hideSlider() {
      sliderContainer.style.opacity = "0";
    }
    function showSlider() {
      sliderContainer.style.opacity = "0.8";
      clearTimeout(sliderTimeout);
      sliderTimeout = setTimeout(hideSlider, 3000);
    }
    sliderTimeout = setTimeout(hideSlider, 3000);
    imageContainer.addEventListener("mouseover", showSlider);
    imageContainer.addEventListener("mouseleave", function() {
      sliderTimeout = setTimeout(hideSlider, 3000);
    });
  </script>
</body>
</html>
