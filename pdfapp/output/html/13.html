<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>13.jpg</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    html { scroll-behavior: smooth; }
    a { text-decoration: none; color: #181818; padding: 5px; }
    a:hover { background-color: #d8f6ff; }
    #back-to-top {
      position: fixed; bottom: 30px; right: 30px; width: 50px; height: 50px;
      background-color: rgba(173,216,230,0.7); color: #fff; text-align: center;
      line-height: 50px; border-radius: 50%; font-size: 24px; display: none;
      z-index: 1000; transition: opacity 0.3s; box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    #back-to-top:hover { opacity: 0.9; }
    /* 画像ズーム・ドラッグ用 */
    #current-image {
      transition: transform 0.2s ease;
      backface-visibility: hidden;
      cursor: move;
    }
    /* 矢印ナビゲーション用 */
    .nav-arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      display: none;
      font-size: 2em;
      color: rgba(0, 0, 0, 0.5);
      text-decoration: none;
      user-select: none;
    }
    #image-container:hover .nav-arrow {
      display: block;
    }
    .left-arrow {
      left: 10px;
    }
    .right-arrow {
      right: 10px;
    }
  </style>
</head>
<body style="background-color: #f3f3f3;">
  <div style="text-align:center;">
    
        <div id="image-container" style="position: relative; display: inline-block;">
            <img id="current-image" src="image/13.jpg" alt="13.jpg" style="display:block; width:90%; max-width:90%; margin:0 auto; box-shadow: 0 4px 8px rgba(0,0,0,0.3);">
            <a href="12.html" class="nav-arrow left-arrow">◀</a><a href="14.html" class="nav-arrow right-arrow">▶</a>
            <div id="zoom-slider-container" style="position: absolute; bottom: -30px; left: 50%; transform: translateX(-50%); z-index: 9999; opacity: 0.8; transition: opacity 0.5s;">
                <label style="margin-right: 10px;">－</label>
                <input type="range" id="zoom-slider" min="0.5" max="2" step="0.01" value="1" style="vertical-align: middle;">
                <label style="margin-left: 10px;">＋</label>
            </div>
        </div>
        
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="12.html" rel="prev">&lt; 戻る</a> <a href="14.html" rel="next">進む &gt;</a> <span>13/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
    <div class="text-box" style="background-color: #ffffff; padding: 32px; margin: 50px auto; display:inline-block; text-align:left; max-width:80%; font-size:103%; line-height:1.65;">
      <span style="color: #5a5a5a;">
<strong>スライド内のプレーンテキスト全文</strong><br>
<small>自動抽出のため画像内のテキスト等は表示されない場合があります。</small><br>
<br>
</span>
<!--
**************************************************
****↓↓ テキスト情報 ↓↓***↓↓ここから↓↓****
**************************************************
-->

      DANH SÁCH CÁC HÌNH VẼHình 2.1 Biểu đồ user-case các chức năng của người dùng (có tài khoản) .................. 21Hình 2.2 Biểu đồ user-case các chức năng của khách (chưa có tài khoản) .................. 21Hình 2.3 Biểu đồ user-case các chức năng của bệnh nhân ........................................... 22Hình 2.4 Biểu đồ user-case các chức năng của bác sĩ và trưởng khoa ......................... 23Hình 2.5 Biểu đồ user-case các chức năng của quản trị viên ....................................... 24Hình 2.6 Biểu đồ user-case các chức năng của y tá ...................................................... 25Hình 2.7 Biểu đồ user-case các chức năng của nhà thuốc ............................................ 25Hình 2.8 Sơ đồ tuần tự đăng nhập ................................................................................ 26Hình 2.9 Sơ đồ tuần tự đăng ký .................................................................................... 26Hình 2.10 Sơ đồ tuần tự quên mật khẩu ....................................................................... 27Hình 2.11 Sơ đồ tuần tự tạo lịch trống của bác sĩ ......................................................... 28Hình 2.12 Sơ đồ tuần tự đăng ký lịch hẹn khám dành cho bệnh nhân ......................... 29Hình 2.13 Sơ đồ tuần tự từ chối lịch hẹn khám của y tá ............................................... 30Hình 2.14 Sơ đồ tuần tự chấp nhận lịch hẹn khám của y tá ......................................... 30Hình 2.15 Sơ đồ tuần tự huỷ lịch hẹn khám của bệnh nhân ......................................... 31Hình 2.16 Sơ đồ tuần tự xem lịch sử hẹn khám của bệnh nhân ................................... 32Hình 2.17 Sơ đồ tuần tự tạo kết quả chuẩn đoán của bác sĩ ......................................... 33Hình 2.18 Sơ đồ tuần tự tạo đơn thuốc của bác sĩ ........................................................ 33Hình 2.19 Sơ đồ tuần tự gửi đơn thuốc đến nhà thuốc ................................................. 34Hình 2.20 Sơ đồ tuần tự tạo hoá đơn ............................................................................ 35Hình 2.21 Sơ đồ lớp xác thực ....................................................................................... 36Hình 2.22 Sơ đồ lớp quản lý lịch làm việc của bác sĩ .................................................. 37Hình 2.23 Sơ đồ lớp quản lý lịch hẹn khám của bác sĩ ................................................ 37Hình 2.24 Sơ đồ lớp quản lý lịch hẹn khám của trưởng khoa ...................................... 38Hình 2.25 Sơ đồ lớp quản lý lịch hẹn khám của y tá .................................................... 39Hình 2.26 Sơ đồ lớp quản lý kết quả kiểm tra của y tá ................................................. 40Hình 2.27 Sơ đồ lớp đặt lịch hẹn khám ........................................................................ 41Hình 2.28 Sơ đồ lớp thông báo ..................................................................................... 42Hình 2.29 Sơ đồ lớp quản lý hồ sơ kết quả y tế ............................................................ 43vii
      <br>
<!--
**************************************************
****↑↑ テキスト情報 ↑↑***↑↑ここまで↑↑****
**************************************************
-->

    </div>
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="12.html" rel="prev">&lt; 戻る</a> <a href="14.html" rel="next">進む &gt;</a> <span>13/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
  </div>
  <a id="back-to-top" href="#">↑</a>
  <script>
    // キーボードショートカット
    document.addEventListener('keydown', function(event) {
      if (event.key === 'ArrowLeft') {
        var prevLink = document.querySelector('a[rel="prev"]');
        if (prevLink) { window.location.href = prevLink.href; }
      }
      if (event.key === 'ArrowRight') {
        var nextLink = document.querySelector('a[rel="next"]');
        if (nextLink) { window.location.href = nextLink.href; }
      }
    });
    // 上に戻るボタンの表示
    window.onscroll = function() {
      var backToTop = document.getElementById("back-to-top");
      if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
        backToTop.style.display = "block";
      } else {
        backToTop.style.display = "none";
      }
    };
    document.getElementById("back-to-top").addEventListener("click", function(e) {
      e.preventDefault();
      window.scrollTo({top: 0, behavior: 'smooth'});
    });
    // 画像ズーム・ドラッグ処理
    var slider = document.getElementById("zoom-slider");
    var currentImage = document.getElementById("current-image");

    var currentScale = slider ? parseFloat(slider.value) : 1;
    var currentTranslateX = 0;
    var currentTranslateY = 0;

    function updateTransform() {
      currentImage.style.transform = "translate(" + currentTranslateX + "px, " + currentTranslateY + "px) scale(" + currentScale + ")";
    }

    if (slider && currentImage) {
      slider.addEventListener("input", function() {
        currentScale = parseFloat(this.value);
        updateTransform();
      });
    }

    var isDragging = false;
    var dragStartX = 0;
    var dragStartY = 0;

    if (currentImage) {
      currentImage.addEventListener("mousedown", function(e) {
        isDragging = true;
        dragStartX = e.clientX;
        dragStartY = e.clientY;
        e.preventDefault();
      });

      document.addEventListener("mousemove", function(e) {
        if (isDragging) {
          var deltaX = e.clientX - dragStartX;
          var deltaY = e.clientY - dragStartY;
          dragStartX = e.clientX;
          dragStartY = e.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("mouseup", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });

      currentImage.addEventListener("touchstart", function(e) {
        isDragging = true;
        var touch = e.touches[0];
        dragStartX = touch.clientX;
        dragStartY = touch.clientY;
      });

      document.addEventListener("touchmove", function(e) {
        if (isDragging) {
          var touch = e.touches[0];
          var deltaX = touch.clientX - dragStartX;
          var deltaY = touch.clientY - dragStartY;
          dragStartX = touch.clientX;
          dragStartY = touch.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("touchend", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });
    }

    // スライダー自動非表示処理
    var sliderContainer = document.getElementById("zoom-slider-container");
    var imageContainer = document.getElementById("image-container");
    var sliderTimeout;
    function hideSlider() {
      sliderContainer.style.opacity = "0";
    }
    function showSlider() {
      sliderContainer.style.opacity = "0.8";
      clearTimeout(sliderTimeout);
      sliderTimeout = setTimeout(hideSlider, 3000);
    }
    sliderTimeout = setTimeout(hideSlider, 3000);
    imageContainer.addEventListener("mouseover", showSlider);
    imageContainer.addEventListener("mouseleave", function() {
      sliderTimeout = setTimeout(hideSlider, 3000);
    });
  </script>
</body>
</html>
