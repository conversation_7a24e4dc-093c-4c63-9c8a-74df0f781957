Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏeNội dung Text area Nhập nội dung chính của bài viết (có thể địnhBlog dạng rich text)
Tải ảnh (tùy File upload Cho phép đính kèm ảnh minh họa cho bài viếtchọn)
Nút "Gửi Button Nhấn để gửi bài viết đến Admin xem xétduyệt"
Thông báo Message Hiển thị thông báo "Đã gửi bài viết thànhtrạng thái công", hoặc "Bài viết bị từ chối"
Hoạt độngTên Mô tả Thành công Thất bạiTruy cập tạo Bác sĩ vào Hiển thị formBlog giao diện tạo nhập tiêu đề, nộibài viết từ dung và ảnhdashboardNhập nội Nhập tiêu đề, Dữ liệu được gửi Nếu mất kết nối: Hiển thịdung & gửi nội dung, ảnh tới Admin để thông báo “Kết nối mạng bịduyệt và nhấn “Gửi duyệt nội dung mất. Vui lòng thử lại sau.”
duyệt”
Admin duyệt Admin xem Nếu duyệt: Bàinội dung xét và quyết viết được hiểnBlog định duyệt thị trên hệhoặc từ chối thốngNếu từbài viết chối: Hiển thịthông báo chobác sĩGửi thông Hệ thống Hiển thị: “Bàibáo trạng thông báo cho viết đã đượcthái bác sĩ biết kết duyệt” hoặc “Bàiquả xét duyệt viết bị từ chối”
Bảng 3.14 Mô tả chức năng tạo bài viếtSinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 84