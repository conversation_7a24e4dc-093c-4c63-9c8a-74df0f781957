Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏeCHƯƠNG 2: PHÂN TÍCH VÀ THIẾT KẾ HỆ THỐNG2.1 Phân tích yêu cầu2.1.1 Tổng quanWebsite hỗ trợ khám chữa bệnh tích hợp chatbot tư vấn sức khoẻ cungcấp trải nghiệm đặt lịch khám và quản lý hồ sơ sức khỏe trực tuyến với giao diệnthân thiện, hỗ trợ tìm kiếm bác sĩ, đặt lịch hẹn, thanh toán online và quản lý kết quảy tế. Hệ thống chatbot AI tự động tư vấn, giải đáp thông tin về lịch hẹn, gói dịch vụvà hồ sơ bệnh án, giúp tiết kiệm thời gian và giảm tải cho nhân viên. Website cũngtích hợp kiểm duyệt nội dung để đảm bảo môi trường tương tác an toàn và là<PERSON>, đồng thời cung cấp các công cụ thống kê phục vụ quản lý và ra quyết địnhhiệu quả.
Các yêu cầu chức năng cần có:
- Quản lý về các gói dịch vụ chăm sóc sức khỏe, bác sĩ, y tá, trưởngkhoa và nhân viên nhà thuốc trên website.
- Quản lý lịch hẹn khám.
- Quản lý hồ sơ bệnh án, kết quả xét nghiệm, đơn thuốc và blog chuyênmôn.
- Thực hiện các cuộc gọi video tư vấn từ xa.
- Chatbot hỗ trợ phản hồi tự động và cung cấp các khuyến nghị chămsóc sức khỏe.
- Tìm kiếm và xem thông tin bác sĩ, dịch vụ y tế; đặt lịch hẹn trực tuyếnvới bác sĩ hoặc gói dịch vụ mong muốn.
- Xem lịch sử đặt lịch hẹn, cập nhật thông tin hồ sơ cá nhân, thanh toánan toàn và xuất hóa đơn điện tử.
- Xem, bình luận, phản hồi trên blog.
2.2 Phân tích và thiết kế hệ thống2.2.1 Tác nhânSinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 19