Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏe
- Persona (<PERSON><PERSON><PERSON> cách/Định danh): <PERSON><PERSON><PERSON> định vai trò, phong cách giao tiế<PERSON>,
phạm vi kiến thức và giới hạn của agent.
- Prompt (Lời nhắc): <PERSON><PERSON><PERSON> hướng cách agent xử lý yêu cầu, trả lời câu hỏihoặc thực hiện tác vụ.
- Skill (Kỹ năng/Plugin): Mở rộng khả năng của agent bằng cách tích hợpcác plugin như tìm kiếm Google, truy xu<PERSON>t dữ liệ<PERSON>, sinh ảnh, phân tích<PERSON><PERSON><PERSON> cáo, v.v.
- Workflow (Quy trình tự động): Xây dựng luồng xử lý phức tạp bằng cáchkết nối nhiều tác vụ, plugin, hoặc thậm chí nhiều agent cùng phối hợp gi<PERSON>iquyết một bài toán lớn.
- Trigger (Kích ho<PERSON>): Cho phép agent tự động thực hiện tác vụ dựa trên sựkiện, thời gian hoặc điều kiện hội thoại.
- Memory (Bộ nhớ): Lưu trữ thông tin ngắn hạn và dài hạn để tăng khảnăng hiểu ngữ cảnh và cá nhân hóa trải nghiệm. Chế độ multi-agent – Độingũ AI phối hợp
- Coze hỗ trợ chế độ multi-agent, cho phép xây dựng đội ngũ agent AI, mỗiagent đảm nhiệm một vai trò chuyên biệt. Các agent này phối hợp thôngqua workflow, giúp giải quyết các tác vụ phức tạp như một nhóm chuyêngia thực thụ1.4.3 Cơ sở dữ liệuMongoDB là một hệ quản trị cơ sở dữ liệu NoSQL mã nguồn mở, hướng tàiliệu (document-oriented), được thiết kế để lưu trữ và quản lý khối lượng dữ liệu lớnvới hiệu năng cao, tính linh hoạt và khả năng mở rộng vượt trội.
Đặc điểm nổi bật của MongoDB:
- Hướng tài liệu (Document-Oriented): Dữ liệu trong MongoDB đượclưu dưới dạng các tài liệu (document), sử dụng cấu trúc key-valuetương tự như JSON (thực tế là BSON – Binary JSON). Mỗi tài liệu cóthể chứa nhiều trường dữ liệu với kiểu dữ liệu khác nhau, kể cả mảngvà tài liệu lồng nhau, giúp lưu trữ dữ liệu phức tạp và linh hoạt hơn sovới mô hình bảng của cơ sở dữ liệu quan hệ.
- Không cần schema cố định (Schema-less): Các tài liệu trong cùng mộtcollection (tương tự bảng trong RDBMS) không bắt buộc phải có cùngSinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 17