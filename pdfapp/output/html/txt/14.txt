Hình 2.30 Sơ đồ lớp quản lý đơn thuốc ........................................................................ 43Hình 2.31 Sơ đồ lớp quản lý hoá đơn ........................................................................... 44Hình 2.32 Sơ đồ lớp quản lý tài khoản ......................................................................... 45Hình 2.33 <PERSON>ơ đồ lớp quản lý bài viết ............................................................................ 46Hình 2.26 Hình 2.34 Sơ đồ lớp quản lý dịch vụ ........................................................... 47Hình 2.35 Sơ đồ lớp quản lý bác sĩ ............................................................................... 48Hình 2.36 Sơ đồ lớp quản lý hồ sơ người dùng ............................................................ 49Hình 2.37 Sơ đồ lớp thanh toán .................................................................................... 50Hình 2.38 Cơ sở dữ liệu ................................................................................................ 51Hình 3.1 Sơ đồ triển khai hệ thống website .................................................................. 61Hình 3.2 Nhập thông tin tạo Agent ............................................................................... 62Hình 3.3 Tạo luồng hội thoại cho chatbot .................................................................... 63Hình 3.4 Kiểm tra phản hồi của chatbot dựa trên triệu chứng người dùng .................. 64Hình 3.5 Triển khai website trên máy ảo Vultr ............................................................ 65Hình 3.6 Giao diện màn hình đăng nhập ...................................................................... 65Hình 3.7 Giao diện màn hình đăng ký .......................................................................... 67Hình 3.8 Giao diện màn hình quên mật khẩu ............................................................... 68Hình 3.9 Giao diện màn hình tạo lịch trống của bác sĩ ................................................. 70Hình 3.10 Giao diện màn hình đặt lịch hẹn khám dành cho bệnh nhân ....................... 71Hình 3.11 Giao diện màn hình từ chối lịch hẹn khám .................................................. 72Hình 3.12 Giao diện màn hình xác nhận lịch hẹn khám ............................................... 73Hình 3.13 Giao diện màn hình huỷ lịch hẹn khám ....................................................... 75Hình 3.14 Giao diện màn hình xem lịch sử hẹn khám ................................................. 76Hình 3.15 Giao diện màn hình tạo kết quả chuẩn đoán ................................................ 77Hình 3.16 Giao diện màn hình tạo đơn thuốc ............................................................... 79Hình 3.17 Giao diện màn hình nhà thuốc nhận đơn thuốc ........................................... 80Hình 3.18 Giao diện màn hình tạo hoá đơn .................................................................. 81Hình 3.19 Giao diện màn hình tạo bài viết ................................................................... 83Hình 3.20 Giao diện màn hình khám bệnh trực tuyến .................................................. 85Hình 3.21 Giao diện màn hình nhắn tin với chatbot ..................................................... 86viii