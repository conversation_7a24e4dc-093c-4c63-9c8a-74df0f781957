Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏetiêu được xác định trước. Agent có thể tương tác với môi trường, thu thập dữ liệu,
phân tích thông tin và tự ra quyết định để đạt được mục tiêu mà không cần sự canthiệp liên tục của con người.
Cách hoạt động của AI AgentQuy trình hoạt động của một AI agent thường bao gồm các bước sau:
- Tiếp nhận mục tiêu: Agent nhận mục tiêu hoặc nhiệm vụ từ người dùnghoặc hệ thống.
- Thu thập dữ liệu: Agent thu thập thông tin từ môi trường thông qua cácgiao diện phần mềm (API, webhook, dữ liệu người dùng nhập vào, v.v.)
hoặc cảm biến.
- Xử lý và phân tích: Agent sử dụng các mô hình AI (như LLM) để phântích dữ liệu, hiểu ngữ cảnh và xác định các hành động phù hợp.
- Ra quyết định: Dựa trên phân tích, agent tự động lựa chọn hành độngtối ưu để tiến gần hơn đến mục tiêu đã đặt ra.
- Thực thi hành động: Agent thực hiện hành động (gửi tin nhắn, gọi API,
cập nhật dữ liệu, v.v.).
- Phản hồi và học hỏi: Agent đánh giá kết quả, nhận phản hồi từ môitrường hoặc người dùng để cải thiện hiệu suất trong tương lai.
- Agent AI nổi bật ở khả năng tự động hóa, ra quyết định độc lập, họchỏi liên tục và có thể phối hợp với các agent khác để giải quyết các tácvụ phức tạp.
1.4 Công nghệ sử dụng1.4.1 Ngôn ngữ lập trìnhNode.js[5]: là một môi trường chạy JavaScript mã nguồn mở, đa nền tảng,
được xây dựng trên engine V8 của Google Chrome. Khác với JavaScript truyềnthống chỉ chạy trên trình duyệt, Node.js cho phép lập trình viên sử dụng JavaScriptđể phát triển các ứng dụng phía máy chủ (backend).
Đặc điểm nổi bật của Node.js:
- Kiến trúc bất đồng bộ (asynchronous) và hướng sự kiện (event-
driven): Node.js xử lý nhiều kết nối đồng thời mà không bị chặn luồng,
lý tưởng cho các ứng dụng thời gian thực, chat, streaming, IoT, v.v..
Sinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 15