Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏe
- Tích hợp và đồng bộ hóa dịch vụ: Website hợp nhất các chức năng thiết yế<PERSON>h<PERSON> đặt lịch hẹn, quản lý hồ sơ y tế, giao tiếp giữa bệnh nhân và bác <PERSON> (<PERSON><PERSON><PERSON><PERSON>, bì<PERSON> luận, gọi video) trong một nền tảng duy nhất. Điều này tạo nên mộttrải nghiệm liền mạch, gi<PERSON><PERSON> bệnh nhân dễ dàng quản lý các thông tin sức khỏevà nâng cao chất lượng tương tác với hệ thống y tế.
3. T<PERSON>h năngĐối với admin:
- X<PERSON>, sửa, x<PERSON><PERSON>, cập nhật thông tin về các gói dịch vụ chăm sóc sức khỏe,
b<PERSON><PERSON>, y tá, trưởng khoa và nhân viên nhà thuốc trên website.
Đ<PERSON><PERSON> với bác sĩ, y tá và trưởng khoa:
- Được cung cấp chức năng tạo, xem, chỉnh sửa và xóa lịch hẹn khám, đảmbảo lịch hẹn luôn được quản lý hiệu quả.
- Có thể chấp nhận, từ chối và xem lịch sử yêu cầu đặt lịch hẹn của bệnhnhân.
- Quản lý hồ sơ bệnh án, kết quả xét nghiệm, đơn thuốc và blog chuyênmôn.
- Thực hiện các cuộc gọi video tư vấn từ xa, tăng cường tương tác với bệnhnhân.
- Được chatbot hỗ trợ phản hồi tự động và cung cấp các khuyến nghị chămsóc sức khỏe.
Đối với người dùng đã có tài khoản (bệnh nhân):
- Tìm kiếm và xem thông tin bác sĩ, dịch vụ y tế; đặt lịch hẹn trực tuyếnvới bác sĩ hoặc gói dịch vụ mong muốn.
- Xem lịch sử đặt lịch hẹn, cập nhật thông tin hồ sơ cá nhân, thanh toán antoàn và xuất hóa đơn điện tử.
- Bình luận, phản hồi trên blog và được chatbot hỗ trợ tư vấn, giải đáp thắcmắc sức khỏe 24/7.
- Sử dụng tính năng gọi video để tham vấn y tế từ xa, tiết kiệm thời gian vàtăng cường khả năng tiếp cận.
Đối với người dùng chưa có tài khoản:
- Xem thông tin cơ bản về các dịch vụ chăm sóc sức khỏe, bác sĩ, blogchuyên môn và bình luận của người dùng khác.
- Không được đặt lịch hẹn trực tuyến hoặc tham gia vào hệ thống nhắn tin,
video call.
Sinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 11