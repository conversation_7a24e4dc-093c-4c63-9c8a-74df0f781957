概要課題名: 健康相談チャットボットを統合した診療支援ウェブサイトの構築実施する学生: Chau Die<PERSON> Hoang ( チャウ・ディエム・ホアン)
学生証番号: 102210036 クラス: 21TCLC_Nhat1現在、オンラインで診療予約を行い、医療サービスとチャットボットでやり取りするニーズがますます高まっています。健康相談チャットボットを統合した診療支援ウェブサイトを構築することは、ユーザーのニーズに応えるだけでなく、医療分野におけるデジタルトランスフォーメーションの潮流にも合致しています。このウェブサイトは、ユーザーに診療所、サービス、診療スケジュール、医師に関する詳細かつ信頼性の高い情報を提供します。ユーザーは自分のニーズや予算に合わせたサービスを簡単に探すことができます。さらに、統合されたチャットボットにより、ユーザーは視覚的かつ迅速に検索、予約、健康相談が可能です。透明性のある情報提供により、ユーザーが最適な診療サービスを比較・選択する手助けをします。
「健康相談チャットボットを統合した診療⽀援ウェブサイトの構築」という本テーマは、最新の技術プラットフォームを⽤いて開発されています。バックエンドは Node.js で構築され、⾼いパフォーマンスと拡張性を確保しています。フロントエンドは React、HTML、CSS、JavaScript を活⽤し、ユーザー体験を最適化するためにフレンドリーなインターフェースを設計しています。
特に、チャットボットは ChatGPT をエージェントとして統合し、先進的な⾃
然⾔語処理（NLP）技術を⽤いています。チャットボットはユーザーとインテリジェントに対話し、健康相談や質問に迅速かつ効果的に対応します。
• 序章と第 1 章では、実際のニーズの分析、テーマ実施の動機、目的、プロジェクトの機能、ターゲットユーザーや利用範囲を述べるとともに、
理論的基礎や使用するモデルについて説明します。
• 第 2章では、業務分析、システム設計、データベース設計を行います。
• 第 3 章では、システムの実装、評価、実現された機能について記述します。
• 結論では、達成された成果、限界点、今後の発展方向について述べます。
私は、このウェブサイトがさらに改善されるよう、先生方や友人たちからのご意見をいただけることを大変願っています。