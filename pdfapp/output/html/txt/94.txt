Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏeKẾT LUẬN VÀ HƯỚNG PHÁT TRIỂN1. Kết quả đạt được1.1 Về mặt lý thuyết
- Tìm hiểu thêm được về AI Agent và mô hình ngôn ngữ lớn
- Hiểu thêm về triển khai được một website hoàn chỉnh dành cho mọi thiếtbị.
- Nâng cao khả năng phân tích và thiết kế một website thành nhiều thànhphần.
1.2 Về mặt ứng dụng
- Đ<PERSON> hoàn thành được trên hầu hết các yêu cầu chức năng đã đề ra.
- Giao diện ứng dụng dễ nhìn, phù hợp với mọi đối tượng.
- Nhờ vào việc lựa chọn các công nghệ phù hợp,website có khả năng chịutải tốt và mở rộng dễ dàng.
2. Hạn chế
- Về mặt ứng dụng: Mặc dù website dành cho người dùng về cơ bản đãhoàn thành nhưng một số giao diện vẫn chưa hoàn thành để phù hợp vớithiết bị mobile. Ngoài ra quản trị viên cần bổ sung một số chức năng đểcó thể quản lý trang website tốt hơn.
3. Hướng phát triển
- Tối ưu trải nghiệm người dùng trên thiết bị di động: Sử dụng các thư việnresponsive hoặc xây dựng thêm ứng dụng mobile (Flutter, React Native)
để đảm bảo trải nghiệm đồng nhất trên mọi thiết bị.
- Nâng cấp hệ thống quản trị: Bổ sung các chức năng như thống kê báo cáo,
phân quyền theo vai trò (role-based access), kiểm duyệt nội dung và giámsát hoạt động hệ thống.
- Đa ngôn ngữ hóa hệ thống: Hỗ trợ tiếng Việt, tiếng Anh để mở rộng đốitượng người dùng; đồng thời tích hợp thêm các công cụ dịch tự động chomô hình AI.
- Nâng cao khả năng dự đoán bệnh: Sử dụng các mô hình AI tiên tiến hơn,
kết hợp với dữ liệu y tế chuẩn hóa từ các nguồn như ICD-10, đồng thờicải thiện đầu vào và biểu mẫu thu thập triệu chứng để đảm bảo tính chínhxác và an toàn.
- Bảo mật và quyền riêng tư: Áp dụng các biện pháp bảo mật nghiêm ngặtnhư mã hóa dữ liệu, xác thực đa yếu tố (2FA), log theo dõi hoạt động đểđảm bảo an toàn thông tin y tế cá nhân.
Sinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 88