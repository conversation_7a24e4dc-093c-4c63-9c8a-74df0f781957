Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏeCHƯƠNG 1: CƠ SỞ LÝ THUYẾT1.1 Tổng quan về website
“Website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏe” đượcxây dựng nhằm mang đến trải nghiệm khám chữa bệnh trực tuyến tiện lợi, nhanhchóng và hiệu quả cho người dùng. Website được thiết kế với giao diện hiện đại, thânthiện và dễ sử dụng, cho phép bệnh nhân tìm kiếm, khám phá các gói dịch vụ chămsóc sức khỏe, lựa chọn bác sĩ hoặc đặt lịch hẹn mà không cần đến cơ sở y tế. <PERSON><PERSON><PERSON><PERSON>, người dùng có thể thanh toán trực tuyến thông qua các phương thức thanh toánan toàn và tiện lợi.
Bên cạnh chức năng đặt lịch hẹn, website còn cho phép người dùng quản lýhồ sơ y tế cá nhân, theo dõi lịch sử các cuộc hẹn, hồ sơ kết quả xét nghiệm và đơnthuốc. Đặc biệt, website tích hợp một hệ thống chatbot thông minh hỗ trợ tư vấn sứckhỏe tức thì. Chatbot hoạt động như một trợ lý ảo giúp người dùng nhanh chóng nhậnđược phản hồi về các gói khám, bác sĩ phù hợp và cung cấp thông tin y tế cơ bản mộtcách chính xác và kịp thời.
Chatbot đóng vai trò quan trọng trong việc nâng cao trải nghiệm người dùng,
tiết kiệm thời gian tìm kiếm và giúp giảm tải công việc hành chính cho các nhân viêny tế. Bên cạnh đó, website cũng tích hợp hệ thống kiểm duyệt bình luận tự động, đảmbảo rằng các phản hồi, chia sẻ của người dùng về dịch vụ chăm sóc sức khỏe luônmang tính xây dựng và tích cực. Các bình luận không phù hợp hoặc mang tính chấttiêu cực sẽ được tự động lọc bỏ hoặc chờ xét duyệt bởi quản trị viên, tạo nên môitrường giao tiếp trực tuyến lành mạnh và an toàn cho tất cả người dùng.
1.2 Mô hình ngôn ngữ lớnMô hình ngôn ngữ lớn (Large Language Model)[2] là một loại mô hình trí tuệnhân tạo được huấn luyện trên kho dữ liệu văn bản khổng lồ nhằm hiểu, tạo và xửlý ngôn ngữ tự nhiên giống như con người. Điểm đặc biệt của LLM là khả năng dựđoán từ tiếp theo trong chuỗi văn bản, cho phép nó trả lời câu hỏi, tạo nội dung tựđộng, hiểu ngữ cảnh và thực hiện hội thoại phức tạp.
Khác với các mô hình truyền thống, LLM sử dụng kiến trúc mạng nơ-ron sâu,
nổi bật là Transformer, với hàng tỷ tham số – tức là các liên kết số học giữa cácnơ-ron. Điều này giúp mô hình học được các mối quan hệ phức tạp và ý nghĩa tinhtế trong ngôn ngữ, từ đó xử lý và tạo ra văn bản tự nhiên một cách ấn tượng.
Cơ chế hoạt động của LLMSinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 13