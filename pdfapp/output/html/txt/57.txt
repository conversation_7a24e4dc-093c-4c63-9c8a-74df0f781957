Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏe2.3.2 Cơ sở dữ liệuHeadOfDepartment Doctor Nurse Adminid varchar(30) 00....11 id varchar(30) 0 1 ..1 id varchar(30) 0..1 id varchar(30)
password varchar(30) password varchar(30) password varchar(30) password varchar(30)
fullname varchar(80) fullname varchar(80) fullname varchar(80) fullname varchar(80)
roleID varchar(50) roleID varchar(50) roleID varchar(50) roleID varchar(50)
specialization varchar(50) specialization varchar(50) specialization varchar(50) email varchar(80)
experience int experience int experience int imageUrl varchar(255)
email varchar(80) email varchar(80) email varchar(80) googleId varchar(50)
imageUrl varchar(255) imageUrl varchar(255) imageUrl varchar(255) propicId varchar(30)
googleId varchar(50) googleId varchar(50) googleId varchar(50) dob datepropicId varchar(30) propicId varchar(30) propicId varchar(30) gender varchar(10)
dob date dob date dob date phone varchar(20)
gender varchar(10) gender varchar(10) gender varchar(10) address varchar(100)
phone varchar(20) phone varchar(20) phone varchar(20) city varchar(100)
address varchar(100) address varchar(100) address varchar(100) country varchar(100)
city varchar(100) city varchar(100) city varchar(100) bio varchar(800)
country varchar(100) country varchar(100) country varchar(100) verified booleanbio varchar(800) bio varchar(800) bio varchar(800) active booleanverified boolean verified boolean verified booleanactive boolean active boolean active booleanCommentid varchar(30)
*
Patient Pharmacy Blog userId varchar(30)
00000..........11111 0..1 0..1 *
id varchar(30) id varchar(30) title varchar(100) blogId varchar(100)
fullname varchar(80) location varchar(100) description varchar(1000) content varchar(1000)
email varchar(80) medicines varchar(850) author varchar(100) createdAt datetimephone varchar(20) specialization varchar(100) updatedAt datetimeaddress varchar(100) media varchar(100) reported booleanDoctorScheduledob date slug varchar(100)
*
doctorId varchar(30)
gender varchar(10) likes intavailableSlots varchar(1000) Vitalscity varchar(100) comments varchar(1000)
*
appointmentId varchar(30)
country varchar(100) visibility varchar(10)
*
userId varchar(30)
bio varchar(800) MedicalResultpulse varchar(20)
verified boolean appointmentId varchar(30) *
Appointment bloodPressure varchar(20)
active boolean docResults varchar(1000) 111 11id varchar(30) temperature varchar(20)
testResults varchar(1000) *
userId varchar(30) weight varchar(20)
notes varchar(250) *
Notification doctorId varchar(30) height varchar(20)
* *
userId varchar(30) nurseId varchar(30) generalCondition varchar(100)
message varchar(255) date datetype varchar(50) time timeServiceisRead boolean status varchar(50)
name varchar(30)
relatedId varchar(30) symptoms varchar(1000)
description varchar(200)
diagnosis varchar(1000)
department varchar(100)
medicalHistory varchar(1000)
Prescription price numeric(8,2)
*
appointmentId varchar(30) status varchar(10)
*
doctorId varchar(30) Tests
*
medicinesName varchar(200) appointmentId varchar(30)
*
quantity varchar(20) userId varchar(30)
usage varchar(200) testType varchar(100)
testResults varchar(1000)
Bill
*
appointmentId varchar(30)
userId varchar(30)
*
doctorId varchar(30)
*
pharmacyId varchar(30)
dateIssued datetimepaymentStatus varchar(20)
paymentMethod varchar(20)
totalAmount numeric(8,2)
Hình 2.38 Cơ sở dữ liệuSinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 51