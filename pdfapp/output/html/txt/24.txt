Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏecấu trú<PERSON>, cho phép lưu trữ dữ liệu đa dạng, dễ dàng mở rộng và thayđổi cấu trúc khi cần thiết mà không ảnh hưởng đến dữ liệu cũ.
1.4.4 Máy chủ ảo VultrVultr là một nhà cung cấp dịch vụ máy chủ ảo (cloud VPS) và hạ tầng điệntoán đám mây, tương tự như các nền tảng như DigitalOcean, Linode, hay AWS
(Amazon Web Services) nhưng với giao diện đơn giản hơn và giá cả phải chăng.
Một số điểm nổi bật về Vultr:
- C<PERSON> cấp VPS (máy chủ ảo) trên nền tảng cloud với nhiều cấu hìnhv<PERSON> vị trí máy chủ toàn cầu.
- Hỗ tr<PERSON> triển khai nhanh các hệ điều hành <PERSON>h<PERSON>, CentOS,
Debian, Windows và cả ứng dụng (LAMP, WordPress, Docker…).
- Giao diện đơn giản, dễ sử dụng, thích hợp cho cả người mới và lậptrình viên chuyên nghiệp.
- Giá cả hợp lý, có gói VPS chỉ từ 2.5 – 5 USD/tháng, phù hợp với sinhviên, freelancer, startup.
- Thanh toán linh hoạt, hỗ trợ thẻ tín dụng, PayPal, và đôi khi cảvoucher hoặc gift code.
Ứng dụng phổ biến của Vultr:
- Hosting website (WordPress, Laravel, Django…)
- Triển khai API, backend hoặc ứng dụng web
- Host chatbot, hệ thống AI nhỏ
- Dùng làm môi trường học tập, test server, CI/CD1.5 Kết luậnTrong chương cơ sở lí thuyết, những lí thuyết về mô hình ngôn ngữ lớn,
AI agent, công nghệ sử dụng (Node.js, React) đã được giới thiệu. Dựa trênnhững cơ sở lý thuyết này thì có thể tiếp tục triển khai các chức năng màwebsite đã đề ra.
Sinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 18