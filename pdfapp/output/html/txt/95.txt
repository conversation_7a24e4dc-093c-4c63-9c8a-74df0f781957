Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏeTÀI LIỆU THAM KHẢO
[1] https://tienphong.vn/khao-sat-cua-herbalife-tai-khu-vuc-chau-a-thai-binh-
duong-post1704274.tpo
[2] https://vnptai.io/vi/blog/detail/llm-la-gi
[3] https://www.elastic.co/what-is/large-language-models
[4] <PERSON>, “What are AI agents?”, IBM, 2025
[5] https://nodejs.org/en/learn/getting-started/introduction-to-nodejs
[6] https://www.w3schools.com/react/react_intro.asp]
[7] https://www.salesmartly.com/en/blog/docs/coze-ai-agent/
[8] <PERSON><PERSON>, <PERSON>. “Are Generative AI and Large Language Models the SameThing?” Quiq, 12/5/2023, quiq.com/blog/generative-ai-vs-large-language-models/.
[9] <PERSON><PERSON>, <PERSON>. “In generative AI legal Wild West, the courtroom battles are justgetting started,” CNBC, 3/7/2023, https://www.cnbc.com/2023/04/03/in-
generative-ai-legal-wild-west-lawsuits-are-just-getting-started.html
[10] Getty Images Statement, Getty Images,
2023, https://newsroom.gettyimages.com/en/getty-images/getty-images-
statement
[11] https://db-engines.com/en/system/Couchbase%3BMongoDB
[12] https://render.com/
[13] SaleSmartly , How to Build a Cross-Border AI Chatbot with Coze: AutomateSupport và Slash Costs, 2025Sinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 89