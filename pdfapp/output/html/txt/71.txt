Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏemáy chủ). <PERSON><PERSON> <PERSON><PERSON> tạo, hệ thống sẽ cung cấp một địa chỉ IP public vàSSH để truy cập máy chủ.
- Cài đặt môi trường trên máy ảo: Cài đặt môi trường cần thiết để chạychương trình (Git, Nodejs,…).
- Cấu hình Nginx làm reverse proxy: Tạo file cấu hình Nginx
- Trỏ tên miền từ Hostinger về VPS: T<PERSON>y cập trang miền đã mua ởHostinger, cập nhật DNS để trỏ về VPS.
Hình 3.5 Triển khai website trên máy ảo Vultr3.4 Mô tả chức năng và kết quả đạt đượcHình 3.6 Giao diện màn hình đăng nhậpSinh viên thực hiện: <PERSON><PERSON><PERSON>. Hướng dẫn: ThS. <PERSON><PERSON><PERSON><PERSON> 65