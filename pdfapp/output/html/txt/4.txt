TÓM TẮTTên đề tài: X<PERSON>y dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sứckhỏeSinh viên thực hiện: <PERSON><PERSON><PERSON> Diễm HoàngSố thẻ SV: 102210036 Lớp: 21TCLC_Nhat1Hiện nay, nhu cầu đặt lịch khám bệnh trực tuyến và tương tác với các dịch vụ y tếqua chatbot đang ngày càng phổ biến. Việc xây dựng một website hỗ trợ khám chữabệnh, tích hợp chatbot tư vấn sức khỏe không chỉ đáp ứng nhu cầu của người dùng màcòn phù hợp với xu hướng chuyển đổi số trong ngành y tế. Website sẽ cung cấp chongười dùng thông tin chi tiết và đáng tin cậy về các phòng kh<PERSON>m, dị<PERSON> vụ, lịch khám vàb<PERSON><PERSON> s<PERSON>. <PERSON><PERSON> đó, người dùng có thể dễ dàng tìm kiếm dịch vụ phù hợp với nhu cầu vàngân sách của mình. Bên cạnh đó, chatbot tích hợp sẽ hỗ trợ người dùng tìm kiếm, đặtlịch hẹn, và tư vấn sức khỏe một cách trực quan và nhanh chóng. Nhờ việc cung cấpthông tin minh bạch, website sẽ giúp người dùng so sánh và lựa chọn dịch vụ khám chữabệnh tốt nhất.
Đề tài: “Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấnsức khỏe” được phát triển dựa trên nền tảng công nghệ hiện đại. Phần backend đượcxây dựng bằng Node.js, đảm bảo hiệu suất cao và khả năng mở rộng tốt. Giao diện front-
end được thiết kế thân thiện và tối ưu trải nghiệm người dùng bằng cách sử dụng React,
HTML, CSS, và JavaScript. Đặc biệt, chatbot được tích hợp bằng cách tạo Agent vớiChatGPT, sử dụng công nghệ xử lý ngôn ngữ tự nhiên (NLP) tiên tiến. Chatbot mangđến khả năng tương tác thông minh, hỗ trợ người dùng tư vấn sức khỏe và giải đáp thắcmắc một cách nhanh chóng và hiệu quả.
• Trong phần mở đầu và chương 1, đề tài sẽ phân tích nhu cầu thực tế, lý do thựchiện đề tài, mục tiêu, tính năng của dự án, cũng như đối tượng hướng đến vàphạm vi sử dụng của website. Đồng thời, đề tài cũng sẽ trình bày cơ sở lý thuyếtvà các mô hình sử dụng.
• Trong chương 2, đề tài sẽ phân tích nghiệp vụ, thiết kế hệ thống và cơ sở dữ liệu.
• Trong chương 3, đề tài sẽ triển khai, đánh giá và mô tả các chức năng đã đượcthực hiện.
• Phần kết luận sẽ nêu ra những kết quả đạt được, hạn chế và hướng phát triểntrong tương lai.
Em rất mong nhận được các ý kiến đóng góp của quý thầy cô và bạn bè để website ngàycàng được hoàn thiện hơn.