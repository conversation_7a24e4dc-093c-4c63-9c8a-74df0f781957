Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏe3.2.3 Tích hợp API ngoàiCoze hỗ trợ tạo API Node để kết nối đến hệ thống backend:
- GetDoctors: L<PERSON><PERSON> danh sách bác sĩ
- GetDoctorsSchedule: <PERSON><PERSON><PERSON> lịch làm việc của bác sĩ
- BookWithoutDoctor: Đặt lịch hẹn khám chưa lựa chọn bác sĩ
- BookWithDoctor: Đặt lịch hẹn khám với bác sĩ mong muốnThông tin được truyền dưới dạng JSON và phản hồi được xử lý để hiển thị ramàn hình hội thoại.
3.2.4 Kiể<PERSON> thử chatbotChatbot có thể gợi ý phòng ban khám bệnh dựa trên triệu chứng người dùngvà đặt lịch cho người dùng.
Hình 3.4 Kiểm tra phản hồi của chatbot dựa trên triệu chứng người dùng3.3 Môi trường phát triểnTriển khai sản phẩm và vận hành hệ thống web trên nền tảng Vultr. Để đảmbảo tốc độ truy cập ổn định, tối ưu chi phí, cũng như dễ dàng kiểm soát cấu hình hệthống, sử dụng máy chủ ảo (VPS) kết hợp với Nginx làm reverse proxy để triển khaiwebsite có thể truy cập từ Internet. Ngoài ra, tên miền được mua và quản lý quaHostinger, và được trỏ về địa chỉ IP của VPS để hoàn tất quá trình cấu hình.
Quy trình để triển khai hệ thống:
- Tạo máy chủ ảo trên Vultr: Truy cập trang web của Vultr, tạo một máyảo với cấu hình thích hợp (hệ điều hành, vị trí máy chủ, kích thướcSinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 64