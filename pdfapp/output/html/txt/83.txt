Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏeChức năng xem lịch sử hẹn khám được biểu diễn qua bảng dưới đâyGiao diện Lịch sử đặt lịch<PERSON>ô tả Cho phép bệnh nhân xem lại lịch sử các cuộc hẹn đã đặt, baog<PERSON>m thông tin chi tiết về bác sĩ, thời gian và trạng thái.
Truy cập Bệnh nhân nhấn vào mục “Lịch sử đặt lịch” trong thanh điềuhướng.
Nội dung giao diệnMục Loại Mô tảLịch sử đặt Table Hiển thị danh sách các cuộc hẹn với các cột: tênl<PERSON>ch bác sĩ, ng<PERSON>y, gi<PERSON>, trạng tháiXem chi tiết Button/<PERSON> Cho phép bệnh nhân xem thông tin chi tiết củacuộc hẹn từng cuộc hẹnThông báo Message Hiển thị thông báo nếu không có lịch sử hoặchệ thống gặp lỗiHoạt độngTên Mô tả Thành công Thất bạiTruy cập Bệnh nhân Hệ thống hiển thị Không có lịch sử hiện thôngmục “Lịch nhấn vào nút danh sách lịch sử báo: "Bạn chưa có lịch sử đặtsử đặt lịch” “Lịch sử đặt đặt lịch của bệnh lịch"
lịch” trên nhânnavbarXem thông Bệnh nhân Hiển thị chi tiết:
tin chi tiết nhấn vào tên bác sĩ, lý dodòng hoặc hủy (nếu có),
nút “Xem trạng thái cuộcchi tiết” hẹnBảng 3.9 Mô tả chức năng xem lịch sử hẹn khámHình 3.15 Giao diện màn hình tạo kết quả chuẩn đoánSinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 77