Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏevề tình trạngsức khỏeKết thúc Bác sĩ hoặc Hệ thống kết Nếu mất kết nối khi đang kếtcuộc họp bệnh nhân thúc phiên họp, thúc: <PERSON>ệ thống tự động lưunhấn nút “Kết cập nhật trạng và cập nhật khi có lại kết nốithúc cuộc thái cuộc hẹnhọp” thành “Đã hoàntất”
Bảng 3.15 M<PERSON> tả chức năng khám bệnh trực tuyếnHình 3.21 Giao diện màn hình nhắn tin với chatbotChức năng nhắn tin với chatbot được biểu diễn qua bảng dưới đâyGiao diện Nhắn tin với chatbotMô tả Cho phép bệnh nhân trò chuyện với hệ thống AI để mô tả triệuchứng, nhận gợi ý bác sĩ phù hợp và đặt lịch hẹn.
Truy cập Nút “Chat với AI” xuất hiện trên trang chủ bệnh nhân.
Nội dung giao diệnMục Loại Mô tảChat với AI Button Nút “Chat với AI” để bắt đầu cuộc trò chuyệnvới hệ thốngGiao diện trò Chat Box Khung trò chuyện nơi bệnh nhân nhập triệuchuyện chứng và nhận phản hồi từ AIHoạt độngTên Mô tả Thành công Thất bạiBắt đầu chat Bệnh nhân Giao diện trò Nếu không thể kết nối AI:
với AI nhấn nút chuyện mở ra Hiển thị lỗi “Không thể kết
“Chat với AI” nối đến hệ thống AI, vui lòngthử lại sau.”
Sinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 86