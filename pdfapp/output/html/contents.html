<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>目次</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    html {
      scroll-behavior: smooth;
    }
    body {
      background-color: #e9e9e9;
      font-family: Arial, sans-serif;
    }
    .container {
      max-width: 90%;
      margin: 0 auto;
    }
    .title {
      text-align: center;
      font-size: 2em;
      margin: 20px 0;
    }
    .box {
      background-color: #fff;
      border: 1px solid #ccc;
      padding: 50px;
      margin: 20px auto;
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
      text-align: left;
    }
    ol {
      padding-left: 20px;
    }
    li {
      margin-bottom: 10px;
    }
    a {
      text-decoration: none;
      color: #1f1f1f;
      display: block;
      padding: 5px;
      font-size: 112%;  /* 文字サイズを 8% 拡大 */
    }
    a:hover {
      background-color: #d8f6ff;
    }
    /* 上に戻るボタン */
    #back-to-top {
      position: fixed;
      bottom: 30px;
      right: 30px;
      width: 50px;
      height: 50px;
      background-color: rgba(173,216,230,0.7);
      color: #fff;
      text-align: center;
      line-height: 50px;
      border-radius: 50%;
      text-decoration: none;
      font-size: 24px;
      display: none;
      z-index: 1000;
      transition: opacity 0.3s;
    }
    #back-to-top:hover {
      opacity: 0.9;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="title">目次</div>
    <div style="text-align: center; margin: 20px 0;"><a href="1.html" style="margin-right: 20px;">表紙</a><a href="thumbnail.html">サムネイル一覧</a><div style="margin-top: 10px;">全 96 ページ</div></div>
    <div class="box">
      <ol>
        <li style="margin-bottom: 10px;"><a href="1.html">ĐẠI HỌC ĐÀ NẴNGTRƯỜNG ĐẠI HỌC BÁCH 　・・・1ページ</a></li><li style="margin-bottom: 10px;"><a href="2.html">NHẬN XÉT CỦA NGƯỜI HƯỜNG DẪN
......　・・・2ページ</a></li><li style="margin-bottom: 10px;"><a href="3.html">概要課題名: 健康相談チャットボットを統合した診療支援ウェブサイトの構　・・・3ページ</a></li><li style="margin-bottom: 10px;"><a href="4.html">TÓM TẮTTên đề tài: Xây dựng website　・・・4ページ</a></li><li style="margin-bottom: 10px;"><a href="5.html">ĐẠI HỌC ĐÀ NẴNG CỘNG HÒA XÃ HÔI CHỦ　・・・5ページ</a></li><li style="margin-bottom: 10px;"><a href="6.html">　・・・6ページ</a></li><li style="margin-bottom: 10px;"><a href="7.html">LỜI NÓI ĐẦULời đầu tiên em xin cám 　・・・7ページ</a></li><li style="margin-bottom: 10px;"><a href="8.html">CAM ĐOANTôi xin cam đoan:
1. Báo cá　・・・8ページ</a></li><li style="margin-bottom: 10px;"><a href="9.html">MỤC LỤC概要 .........................　・・・9ページ</a></li><li style="margin-bottom: 10px;"><a href="10.html">2.3.1 Sơ đồ lớp ...................　・・・10ページ</a></li><li style="margin-bottom: 10px;"><a href="11.html">DANH SÁCH CÁC BẢNGBảng 2.1 Bảng mô 　・・・11ページ</a></li><li style="margin-bottom: 10px;"><a href="12.html">Bảng 3.14 Mô tả chức năng tạo bài v　・・・12ページ</a></li><li style="margin-bottom: 10px;"><a href="13.html">DANH SÁCH CÁC HÌNH VẼHình 2.1 Biểu 　・・・13ページ</a></li><li style="margin-bottom: 10px;"><a href="14.html">Hình 2.30 Sơ đồ lớp quản lý đơn thu　・・・14ページ</a></li><li style="margin-bottom: 10px;"><a href="15.html">DANH SÁCH CÁC KÝ HIỆU, CHỮ VIẾT TẮT　・・・15ページ</a></li><li style="margin-bottom: 10px;"><a href="16.html">Xây dựng website hỗ trợ khám chữa b　・・・16ページ</a></li><li style="margin-bottom: 10px;"><a href="17.html">Xây dựng website hỗ trợ khám chữa b　・・・17ページ</a></li><li style="margin-bottom: 10px;"><a href="18.html">Xây dựng website hỗ trợ khám chữa b　・・・18ページ</a></li><li style="margin-bottom: 10px;"><a href="19.html">Xây dựng website hỗ trợ khám chữa b　・・・19ページ</a></li><li style="margin-bottom: 10px;"><a href="20.html">Xây dựng website hỗ trợ khám chữa b　・・・20ページ</a></li><li style="margin-bottom: 10px;"><a href="21.html">Xây dựng website hỗ trợ khám chữa b　・・・21ページ</a></li><li style="margin-bottom: 10px;"><a href="22.html">Xây dựng website hỗ trợ khám chữa b　・・・22ページ</a></li><li style="margin-bottom: 10px;"><a href="23.html">Xây dựng website hỗ trợ khám chữa b　・・・23ページ</a></li><li style="margin-bottom: 10px;"><a href="24.html">Xây dựng website hỗ trợ khám chữa b　・・・24ページ</a></li><li style="margin-bottom: 10px;"><a href="25.html">Xây dựng website hỗ trợ khám chữa b　・・・25ページ</a></li><li style="margin-bottom: 10px;"><a href="26.html">Xây dựng website hỗ trợ khám chữa b　・・・26ページ</a></li><li style="margin-bottom: 10px;"><a href="27.html">Xây dựng website hỗ trợ khám chữa b　・・・27ページ</a></li><li style="margin-bottom: 10px;"><a href="28.html">Xây dựng website hỗ trợ khám chữa b　・・・28ページ</a></li><li style="margin-bottom: 10px;"><a href="29.html">Xây dựng website hỗ trợ khám chữa b　・・・29ページ</a></li><li style="margin-bottom: 10px;"><a href="30.html">Xây dựng website hỗ trợ khám chữa b　・・・30ページ</a></li><li style="margin-bottom: 10px;"><a href="31.html">Xây dựng website hỗ trợ khám chữa b　・・・31ページ</a></li><li style="margin-bottom: 10px;"><a href="32.html">Xây dựng website hỗ trợ khám chữa b　・・・32ページ</a></li><li style="margin-bottom: 10px;"><a href="33.html">Xây dựng website hỗ trợ khám chữa b　・・・33ページ</a></li><li style="margin-bottom: 10px;"><a href="34.html">Xây dựng website hỗ trợ khám chữa b　・・・34ページ</a></li><li style="margin-bottom: 10px;"><a href="35.html">Xây dựng website hỗ trợ khám chữa b　・・・35ページ</a></li><li style="margin-bottom: 10px;"><a href="36.html">Xây dựng website hỗ trợ khám chữa b　・・・36ページ</a></li><li style="margin-bottom: 10px;"><a href="37.html">Xây dựng website hỗ trợ khám chữa b　・・・37ページ</a></li><li style="margin-bottom: 10px;"><a href="38.html">Xây dựng website hỗ trợ khám chữa b　・・・38ページ</a></li><li style="margin-bottom: 10px;"><a href="39.html">Xây dựng website hỗ trợ khám chữa b　・・・39ページ</a></li><li style="margin-bottom: 10px;"><a href="40.html">Xây dựng website hỗ trợ khám chữa b　・・・40ページ</a></li><li style="margin-bottom: 10px;"><a href="41.html">Xây dựng website hỗ trợ khám chữa b　・・・41ページ</a></li><li style="margin-bottom: 10px;"><a href="42.html">Xây dựng website hỗ trợ khám chữa b　・・・42ページ</a></li><li style="margin-bottom: 10px;"><a href="43.html">Xây dựng website hỗ trợ khám chữa b　・・・43ページ</a></li><li style="margin-bottom: 10px;"><a href="44.html">Xây dựng website hỗ trợ khám chữa b　・・・44ページ</a></li><li style="margin-bottom: 10px;"><a href="45.html">Xây dựng website hỗ trợ khám chữa b　・・・45ページ</a></li><li style="margin-bottom: 10px;"><a href="46.html">Xây dựng website hỗ trợ khám chữa b　・・・46ページ</a></li><li style="margin-bottom: 10px;"><a href="47.html">Xây dựng website hỗ trợ khám chữa b　・・・47ページ</a></li><li style="margin-bottom: 10px;"><a href="48.html">Xây dựng website hỗ trợ khám chữa b　・・・48ページ</a></li><li style="margin-bottom: 10px;"><a href="49.html">Xây dựng website hỗ trợ khám chữa b　・・・49ページ</a></li><li style="margin-bottom: 10px;"><a href="50.html">Xây dựng website hỗ trợ khám chữa b　・・・50ページ</a></li><li style="margin-bottom: 10px;"><a href="51.html">Xây dựng website hỗ trợ khám chữa b　・・・51ページ</a></li><li style="margin-bottom: 10px;"><a href="52.html">Xây dựng website hỗ trợ khám chữa b　・・・52ページ</a></li><li style="margin-bottom: 10px;"><a href="53.html">Xây dựng website hỗ trợ khám chữa b　・・・53ページ</a></li><li style="margin-bottom: 10px;"><a href="54.html">Xây dựng website hỗ trợ khám chữa b　・・・54ページ</a></li><li style="margin-bottom: 10px;"><a href="55.html">Xây dựng website hỗ trợ khám chữa b　・・・55ページ</a></li><li style="margin-bottom: 10px;"><a href="56.html">Xây dựng website hỗ trợ khám chữa b　・・・56ページ</a></li><li style="margin-bottom: 10px;"><a href="57.html">Xây dựng website hỗ trợ khám chữa b　・・・57ページ</a></li><li style="margin-bottom: 10px;"><a href="58.html">Xây dựng website hỗ trợ khám chữa b　・・・58ページ</a></li><li style="margin-bottom: 10px;"><a href="59.html">Xây dựng website hỗ trợ khám chữa b　・・・59ページ</a></li><li style="margin-bottom: 10px;"><a href="60.html">Xây dựng website hỗ trợ khám chữa b　・・・60ページ</a></li><li style="margin-bottom: 10px;"><a href="61.html">Xây dựng website hỗ trợ khám chữa b　・・・61ページ</a></li><li style="margin-bottom: 10px;"><a href="62.html">Xây dựng website hỗ trợ khám chữa b　・・・62ページ</a></li><li style="margin-bottom: 10px;"><a href="63.html">Xây dựng website hỗ trợ khám chữa b　・・・63ページ</a></li><li style="margin-bottom: 10px;"><a href="64.html">Xây dựng website hỗ trợ khám chữa b　・・・64ページ</a></li><li style="margin-bottom: 10px;"><a href="65.html">Xây dựng website hỗ trợ khám chữa b　・・・65ページ</a></li><li style="margin-bottom: 10px;"><a href="66.html">Xây dựng website hỗ trợ khám chữa b　・・・66ページ</a></li><li style="margin-bottom: 10px;"><a href="67.html">Xây dựng website hỗ trợ khám chữa b　・・・67ページ</a></li><li style="margin-bottom: 10px;"><a href="68.html">Xây dựng website hỗ trợ khám chữa b　・・・68ページ</a></li><li style="margin-bottom: 10px;"><a href="69.html">Xây dựng website hỗ trợ khám chữa b　・・・69ページ</a></li><li style="margin-bottom: 10px;"><a href="70.html">Xây dựng website hỗ trợ khám chữa b　・・・70ページ</a></li><li style="margin-bottom: 10px;"><a href="71.html">Xây dựng website hỗ trợ khám chữa b　・・・71ページ</a></li><li style="margin-bottom: 10px;"><a href="72.html">Xây dựng website hỗ trợ khám chữa b　・・・72ページ</a></li><li style="margin-bottom: 10px;"><a href="73.html">Xây dựng website hỗ trợ khám chữa b　・・・73ページ</a></li><li style="margin-bottom: 10px;"><a href="74.html">Xây dựng website hỗ trợ khám chữa b　・・・74ページ</a></li><li style="margin-bottom: 10px;"><a href="75.html">Xây dựng website hỗ trợ khám chữa b　・・・75ページ</a></li><li style="margin-bottom: 10px;"><a href="76.html">Xây dựng website hỗ trợ khám chữa b　・・・76ページ</a></li><li style="margin-bottom: 10px;"><a href="77.html">Xây dựng website hỗ trợ khám chữa b　・・・77ページ</a></li><li style="margin-bottom: 10px;"><a href="78.html">Xây dựng website hỗ trợ khám chữa b　・・・78ページ</a></li><li style="margin-bottom: 10px;"><a href="79.html">Xây dựng website hỗ trợ khám chữa b　・・・79ページ</a></li><li style="margin-bottom: 10px;"><a href="80.html">Xây dựng website hỗ trợ khám chữa b　・・・80ページ</a></li><li style="margin-bottom: 10px;"><a href="81.html">Xây dựng website hỗ trợ khám chữa b　・・・81ページ</a></li><li style="margin-bottom: 10px;"><a href="82.html">Xây dựng website hỗ trợ khám chữa b　・・・82ページ</a></li><li style="margin-bottom: 10px;"><a href="83.html">Xây dựng website hỗ trợ khám chữa b　・・・83ページ</a></li><li style="margin-bottom: 10px;"><a href="84.html">Xây dựng website hỗ trợ khám chữa b　・・・84ページ</a></li><li style="margin-bottom: 10px;"><a href="85.html">Xây dựng website hỗ trợ khám chữa b　・・・85ページ</a></li><li style="margin-bottom: 10px;"><a href="86.html">Xây dựng website hỗ trợ khám chữa b　・・・86ページ</a></li><li style="margin-bottom: 10px;"><a href="87.html">Xây dựng website hỗ trợ khám chữa b　・・・87ページ</a></li><li style="margin-bottom: 10px;"><a href="88.html">Xây dựng website hỗ trợ khám chữa b　・・・88ページ</a></li><li style="margin-bottom: 10px;"><a href="89.html">Xây dựng website hỗ trợ khám chữa b　・・・89ページ</a></li><li style="margin-bottom: 10px;"><a href="90.html">Xây dựng website hỗ trợ khám chữa b　・・・90ページ</a></li><li style="margin-bottom: 10px;"><a href="91.html">Xây dựng website hỗ trợ khám chữa b　・・・91ページ</a></li><li style="margin-bottom: 10px;"><a href="92.html">Xây dựng website hỗ trợ khám chữa b　・・・92ページ</a></li><li style="margin-bottom: 10px;"><a href="93.html">Xây dựng website hỗ trợ khám chữa b　・・・93ページ</a></li><li style="margin-bottom: 10px;"><a href="94.html">Xây dựng website hỗ trợ khám chữa b　・・・94ページ</a></li><li style="margin-bottom: 10px;"><a href="95.html">Xây dựng website hỗ trợ khám chữa b　・・・95ページ</a></li><li style="margin-bottom: 10px;"><a href="96.html">Xây dựng website hỗ trợ khám chữa b　・・・96ページ</a></li>
      </ol>
    </div>
    <div style="text-align: center; margin: 20px 0;"><a href="1.html" style="margin-right: 20px;">表紙</a><a href="thumbnail.html">サムネイル一覧</a><div style="margin-top: 10px;">全 96 ページ</div></div>
  </div>
  <!-- 上に戻るボタン -->
  <a id="back-to-top" href="#">↑</a>
  <script>
    // スクロール時に上に戻るボタンを表示
    window.onscroll = function() {
      var backToTop = document.getElementById("back-to-top");
      if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
        backToTop.style.display = "block";
      } else {
        backToTop.style.display = "none";
      }
    };
    // クリックで滑らかにトップへスクロール
    document.getElementById("back-to-top").addEventListener("click", function(e) {
      e.preventDefault();
      window.scrollTo({top: 0, behavior: 'smooth'});
    });
  </script>
</body>
</html>
