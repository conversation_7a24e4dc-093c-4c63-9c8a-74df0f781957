<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>16.jpg</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    html { scroll-behavior: smooth; }
    a { text-decoration: none; color: #181818; padding: 5px; }
    a:hover { background-color: #d8f6ff; }
    #back-to-top {
      position: fixed; bottom: 30px; right: 30px; width: 50px; height: 50px;
      background-color: rgba(173,216,230,0.7); color: #fff; text-align: center;
      line-height: 50px; border-radius: 50%; font-size: 24px; display: none;
      z-index: 1000; transition: opacity 0.3s; box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    #back-to-top:hover { opacity: 0.9; }
    /* 画像ズーム・ドラッグ用 */
    #current-image {
      transition: transform 0.2s ease;
      backface-visibility: hidden;
      cursor: move;
    }
    /* 矢印ナビゲーション用 */
    .nav-arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      display: none;
      font-size: 2em;
      color: rgba(0, 0, 0, 0.5);
      text-decoration: none;
      user-select: none;
    }
    #image-container:hover .nav-arrow {
      display: block;
    }
    .left-arrow {
      left: 10px;
    }
    .right-arrow {
      right: 10px;
    }
  </style>
</head>
<body style="background-color: #f3f3f3;">
  <div style="text-align:center;">
    
        <div id="image-container" style="position: relative; display: inline-block;">
            <img id="current-image" src="image/16.jpg" alt="16.jpg" style="display:block; width:90%; max-width:90%; margin:0 auto; box-shadow: 0 4px 8px rgba(0,0,0,0.3);">
            <a href="15.html" class="nav-arrow left-arrow">◀</a><a href="17.html" class="nav-arrow right-arrow">▶</a>
            <div id="zoom-slider-container" style="position: absolute; bottom: -30px; left: 50%; transform: translateX(-50%); z-index: 9999; opacity: 0.8; transition: opacity 0.5s;">
                <label style="margin-right: 10px;">－</label>
                <input type="range" id="zoom-slider" min="0.5" max="2" step="0.01" value="1" style="vertical-align: middle;">
                <label style="margin-left: 10px;">＋</label>
            </div>
        </div>
        
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="15.html" rel="prev">&lt; 戻る</a> <a href="17.html" rel="next">進む &gt;</a> <span>16/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
    <div class="text-box" style="background-color: #ffffff; padding: 32px; margin: 50px auto; display:inline-block; text-align:left; max-width:80%; font-size:103%; line-height:1.65;">
      <span style="color: #5a5a5a;">
<strong>スライド内のプレーンテキスト全文</strong><br>
<small>自動抽出のため画像内のテキスト等は表示されない場合があります。</small><br>
<br>
</span>
<!--
**************************************************
****↓↓ テキスト情報 ↓↓***↓↓ここから↓↓****
**************************************************
-->

      Xây dựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏeMỞ ĐẦU1. Tổng quan vấn đềTrong những năm gần đây, nhu cầu chăm sóc sức khỏe dễ tiếp cận và tăng lênở Việt Nam đã tăng đáng kể. Một cuộc khảo sát do Herbalife thực hiện tại khu vựcChâu Á - Thái Bình Dương, bao gồm 8.000 người trả lời trên 11 thị trường, bao gồmViệt Nam, cho thấy 51% số người tham gia chi tiêu cho các sản phẩm chăm sóc sứckhỏe và thể chất có kế hoạch tăng chi tiêu vào năm 2024, trong đó 64% có ý địnhtăng chi tiêu ít nhất 25%[1]. Cam kết ngày càng tăng này đối với sức khỏe và hạnhphúc nhấn mạnh nhu cầu cấp thiết về các giải pháp chăm sóc sức khỏe sáng tạo vàdễ tiếp cận tại Việt Nam. Tuy nhiên, các phương pháp đặt lịch hẹn chăm sóc sứckhỏe truyền thống vẫn chưa hiệu quả, thường dẫn đến thời gian chờ đợi lâu, xungđột lịch trình và thiếu khả năng tiếp cận đối với những người ở vùng sâu vùng xahoặc vùng chưa được phục vụ đầy đủ.<br>
Một cuộc khảo sát trên toàn quốc được thực hiện tại Việt Nam cho thấy rấtnhiều bệnh nhân gặp khó khăn với việc đặt lịch hẹn theo cách truyền thống, hạn chếvề số lượng bác sĩ được ưu tiên và sự bất tiện khi phải đến trực tiếp các cơ sở chămsóc sức khỏe để đặt lịch. Mặt khác, theo khảo sát, 85,8% trong số 492 bệnh nhânđồng ý rằng việc đặt lịch hẹn trực tuyến có thể tiết kiệm thời gian chờ đợi hơn so vớicác phương pháp truyền thống. Hơn nữa, 63,6% cá nhân được khảo sát bày tỏ sựquan tâm đến việc sử dụng hệ thống đặt lịch hẹn trực tuyến, nhận ra tiềm năng củanó trong việc hợp lý hóa quy trình và tăng cường khả năng tiếp cận.<br>
Nhận ra những thách thức và bối cảnh tiềm năng này, cùng sự đồng ý củaTrường Đại học Bách khoa Đà Nẵng, sự hướng dẫn tận tình của thầy ThS. NguyễnVăn Nguyên và sự động viên, hỗ trợ từ gia đình, bạn bè, em đã thực hiện đề tài: Xâydựng website hỗ trợ khám chữa bệnh tích hợp chatbot để tư vấn sức khỏe.<br>
Website được thiết kế nhằm đặt lịch hẹn trực tuyến dành cho các phòng khámđa khoa, được tích hợp với chatbot hỗ trợ AI để cung cấp tư vấn tức thời để lựa chọndịch vụ, bác sĩ, khuyến nghị và cuộc hẹn phù hợp. Điều này giúp bệnh nhân nhậnđược sự chăm sóc y tế kịp thời đồng thời giảm gánh nặng hành chính cho các cơ sởchăm sóc sức khỏe. hệ sinh thái chăm sóc sức khỏe hiệu quả và dễ tiếp cận hơn.<br>
2. Mục tiêu của đề tàiWebsite hỗ trợ khám chữa bệnh tích hợp chatbot được xây dựng nhằm giải quyếtcác thách thức chính trong khả năng tiếp cận và nâng cao hiệu quả chăm sóc sứckhỏe:<br>
- Khả năng tiếp cận tối ưu: Hệ thống đặt lịch hẹn và tư vấn trực tuyến giúp loạibỏ rào cản về địa lý, cho phép bệnh nhân kết nối với các bác sĩ, y tá và nhânviên y tế từ xa. Điều này đảm bảo rằng ngay cả những người ở vùng sâu vùngxa hoặc khu vực chưa được phục vụ đầy đủ vẫn có thể tiếp cận dịch vụ chămsóc sức khỏe chất lượng một cách kịp thời.<br>
- Hiệu quả về thời gian và trải nghiệm: Chatbot thông minh và các công cụ tựđộng hóa giúp tối ưu hóa quy trình đặt lịch hẹn, giảm thời gian chờ đợi vàsắp xếp công việc hợp lý cho cả bệnh nhân và nhân viên y tế. Chatbot cũnghỗ trợ phản hồi nhanh chóng các thắc mắc của người dùng, đồng thời cungcấp thông tin hữu ích về các dịch vụ, gói khám và đội ngũ y tế.<br>
Sinh viên thực hiện: Châu Diễm Hoàng. Hướng dẫn: ThS. Nguyễn Văn Nguyên 10
      <br>
<!--
**************************************************
****↑↑ テキスト情報 ↑↑***↑↑ここまで↑↑****
**************************************************
-->

    </div>
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="15.html" rel="prev">&lt; 戻る</a> <a href="17.html" rel="next">進む &gt;</a> <span>16/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
  </div>
  <a id="back-to-top" href="#">↑</a>
  <script>
    // キーボードショートカット
    document.addEventListener('keydown', function(event) {
      if (event.key === 'ArrowLeft') {
        var prevLink = document.querySelector('a[rel="prev"]');
        if (prevLink) { window.location.href = prevLink.href; }
      }
      if (event.key === 'ArrowRight') {
        var nextLink = document.querySelector('a[rel="next"]');
        if (nextLink) { window.location.href = nextLink.href; }
      }
    });
    // 上に戻るボタンの表示
    window.onscroll = function() {
      var backToTop = document.getElementById("back-to-top");
      if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
        backToTop.style.display = "block";
      } else {
        backToTop.style.display = "none";
      }
    };
    document.getElementById("back-to-top").addEventListener("click", function(e) {
      e.preventDefault();
      window.scrollTo({top: 0, behavior: 'smooth'});
    });
    // 画像ズーム・ドラッグ処理
    var slider = document.getElementById("zoom-slider");
    var currentImage = document.getElementById("current-image");

    var currentScale = slider ? parseFloat(slider.value) : 1;
    var currentTranslateX = 0;
    var currentTranslateY = 0;

    function updateTransform() {
      currentImage.style.transform = "translate(" + currentTranslateX + "px, " + currentTranslateY + "px) scale(" + currentScale + ")";
    }

    if (slider && currentImage) {
      slider.addEventListener("input", function() {
        currentScale = parseFloat(this.value);
        updateTransform();
      });
    }

    var isDragging = false;
    var dragStartX = 0;
    var dragStartY = 0;

    if (currentImage) {
      currentImage.addEventListener("mousedown", function(e) {
        isDragging = true;
        dragStartX = e.clientX;
        dragStartY = e.clientY;
        e.preventDefault();
      });

      document.addEventListener("mousemove", function(e) {
        if (isDragging) {
          var deltaX = e.clientX - dragStartX;
          var deltaY = e.clientY - dragStartY;
          dragStartX = e.clientX;
          dragStartY = e.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("mouseup", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });

      currentImage.addEventListener("touchstart", function(e) {
        isDragging = true;
        var touch = e.touches[0];
        dragStartX = touch.clientX;
        dragStartY = touch.clientY;
      });

      document.addEventListener("touchmove", function(e) {
        if (isDragging) {
          var touch = e.touches[0];
          var deltaX = touch.clientX - dragStartX;
          var deltaY = touch.clientY - dragStartY;
          dragStartX = touch.clientX;
          dragStartY = touch.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("touchend", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });
    }

    // スライダー自動非表示処理
    var sliderContainer = document.getElementById("zoom-slider-container");
    var imageContainer = document.getElementById("image-container");
    var sliderTimeout;
    function hideSlider() {
      sliderContainer.style.opacity = "0";
    }
    function showSlider() {
      sliderContainer.style.opacity = "0.8";
      clearTimeout(sliderTimeout);
      sliderTimeout = setTimeout(hideSlider, 3000);
    }
    sliderTimeout = setTimeout(hideSlider, 3000);
    imageContainer.addEventListener("mouseover", showSlider);
    imageContainer.addEventListener("mouseleave", function() {
      sliderTimeout = setTimeout(hideSlider, 3000);
    });
  </script>
</body>
</html>
