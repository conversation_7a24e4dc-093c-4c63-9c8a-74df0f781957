<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>14.jpg</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    html { scroll-behavior: smooth; }
    a { text-decoration: none; color: #181818; padding: 5px; }
    a:hover { background-color: #d8f6ff; }
    #back-to-top {
      position: fixed; bottom: 30px; right: 30px; width: 50px; height: 50px;
      background-color: rgba(173,216,230,0.7); color: #fff; text-align: center;
      line-height: 50px; border-radius: 50%; font-size: 24px; display: none;
      z-index: 1000; transition: opacity 0.3s; box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    #back-to-top:hover { opacity: 0.9; }
    /* 画像ズーム・ドラッグ用 */
    #current-image {
      transition: transform 0.2s ease;
      backface-visibility: hidden;
      cursor: move;
    }
    /* 矢印ナビゲーション用 */
    .nav-arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      display: none;
      font-size: 2em;
      color: rgba(0, 0, 0, 0.5);
      text-decoration: none;
      user-select: none;
    }
    #image-container:hover .nav-arrow {
      display: block;
    }
    .left-arrow {
      left: 10px;
    }
    .right-arrow {
      right: 10px;
    }
  </style>
</head>
<body style="background-color: #f3f3f3;">
  <div style="text-align:center;">
    
        <div id="image-container" style="position: relative; display: inline-block;">
            <img id="current-image" src="image/14.jpg" alt="14.jpg" style="display:block; width:90%; max-width:90%; margin:0 auto; box-shadow: 0 4px 8px rgba(0,0,0,0.3);">
            <a href="13.html" class="nav-arrow left-arrow">◀</a><a href="15.html" class="nav-arrow right-arrow">▶</a>
            <div id="zoom-slider-container" style="position: absolute; bottom: -30px; left: 50%; transform: translateX(-50%); z-index: 9999; opacity: 0.8; transition: opacity 0.5s;">
                <label style="margin-right: 10px;">－</label>
                <input type="range" id="zoom-slider" min="0.5" max="2" step="0.01" value="1" style="vertical-align: middle;">
                <label style="margin-left: 10px;">＋</label>
            </div>
        </div>
        
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="13.html" rel="prev">&lt; 戻る</a> <a href="15.html" rel="next">進む &gt;</a> <span>14/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
    <div class="text-box" style="background-color: #ffffff; padding: 32px; margin: 50px auto; display:inline-block; text-align:left; max-width:80%; font-size:103%; line-height:1.65;">
      <span style="color: #5a5a5a;">
<strong>スライド内のプレーンテキスト全文</strong><br>
<small>自動抽出のため画像内のテキスト等は表示されない場合があります。</small><br>
<br>
</span>
<!--
**************************************************
****↓↓ テキスト情報 ↓↓***↓↓ここから↓↓****
**************************************************
-->

      Hình 2.30 Sơ đồ lớp quản lý đơn thuốc ........................................................................ 43Hình 2.31 Sơ đồ lớp quản lý hoá đơn ........................................................................... 44Hình 2.32 Sơ đồ lớp quản lý tài khoản ......................................................................... 45Hình 2.33 Sơ đồ lớp quản lý bài viết ............................................................................ 46Hình 2.26 Hình 2.34 Sơ đồ lớp quản lý dịch vụ ........................................................... 47Hình 2.35 Sơ đồ lớp quản lý bác sĩ ............................................................................... 48Hình 2.36 Sơ đồ lớp quản lý hồ sơ người dùng ............................................................ 49Hình 2.37 Sơ đồ lớp thanh toán .................................................................................... 50Hình 2.38 Cơ sở dữ liệu ................................................................................................ 51Hình 3.1 Sơ đồ triển khai hệ thống website .................................................................. 61Hình 3.2 Nhập thông tin tạo Agent ............................................................................... 62Hình 3.3 Tạo luồng hội thoại cho chatbot .................................................................... 63Hình 3.4 Kiểm tra phản hồi của chatbot dựa trên triệu chứng người dùng .................. 64Hình 3.5 Triển khai website trên máy ảo Vultr ............................................................ 65Hình 3.6 Giao diện màn hình đăng nhập ...................................................................... 65Hình 3.7 Giao diện màn hình đăng ký .......................................................................... 67Hình 3.8 Giao diện màn hình quên mật khẩu ............................................................... 68Hình 3.9 Giao diện màn hình tạo lịch trống của bác sĩ ................................................. 70Hình 3.10 Giao diện màn hình đặt lịch hẹn khám dành cho bệnh nhân ....................... 71Hình 3.11 Giao diện màn hình từ chối lịch hẹn khám .................................................. 72Hình 3.12 Giao diện màn hình xác nhận lịch hẹn khám ............................................... 73Hình 3.13 Giao diện màn hình huỷ lịch hẹn khám ....................................................... 75Hình 3.14 Giao diện màn hình xem lịch sử hẹn khám ................................................. 76Hình 3.15 Giao diện màn hình tạo kết quả chuẩn đoán ................................................ 77Hình 3.16 Giao diện màn hình tạo đơn thuốc ............................................................... 79Hình 3.17 Giao diện màn hình nhà thuốc nhận đơn thuốc ........................................... 80Hình 3.18 Giao diện màn hình tạo hoá đơn .................................................................. 81Hình 3.19 Giao diện màn hình tạo bài viết ................................................................... 83Hình 3.20 Giao diện màn hình khám bệnh trực tuyến .................................................. 85Hình 3.21 Giao diện màn hình nhắn tin với chatbot ..................................................... 86viii
      <br>
<!--
**************************************************
****↑↑ テキスト情報 ↑↑***↑↑ここまで↑↑****
**************************************************
-->

    </div>
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="13.html" rel="prev">&lt; 戻る</a> <a href="15.html" rel="next">進む &gt;</a> <span>14/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
  </div>
  <a id="back-to-top" href="#">↑</a>
  <script>
    // キーボードショートカット
    document.addEventListener('keydown', function(event) {
      if (event.key === 'ArrowLeft') {
        var prevLink = document.querySelector('a[rel="prev"]');
        if (prevLink) { window.location.href = prevLink.href; }
      }
      if (event.key === 'ArrowRight') {
        var nextLink = document.querySelector('a[rel="next"]');
        if (nextLink) { window.location.href = nextLink.href; }
      }
    });
    // 上に戻るボタンの表示
    window.onscroll = function() {
      var backToTop = document.getElementById("back-to-top");
      if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
        backToTop.style.display = "block";
      } else {
        backToTop.style.display = "none";
      }
    };
    document.getElementById("back-to-top").addEventListener("click", function(e) {
      e.preventDefault();
      window.scrollTo({top: 0, behavior: 'smooth'});
    });
    // 画像ズーム・ドラッグ処理
    var slider = document.getElementById("zoom-slider");
    var currentImage = document.getElementById("current-image");

    var currentScale = slider ? parseFloat(slider.value) : 1;
    var currentTranslateX = 0;
    var currentTranslateY = 0;

    function updateTransform() {
      currentImage.style.transform = "translate(" + currentTranslateX + "px, " + currentTranslateY + "px) scale(" + currentScale + ")";
    }

    if (slider && currentImage) {
      slider.addEventListener("input", function() {
        currentScale = parseFloat(this.value);
        updateTransform();
      });
    }

    var isDragging = false;
    var dragStartX = 0;
    var dragStartY = 0;

    if (currentImage) {
      currentImage.addEventListener("mousedown", function(e) {
        isDragging = true;
        dragStartX = e.clientX;
        dragStartY = e.clientY;
        e.preventDefault();
      });

      document.addEventListener("mousemove", function(e) {
        if (isDragging) {
          var deltaX = e.clientX - dragStartX;
          var deltaY = e.clientY - dragStartY;
          dragStartX = e.clientX;
          dragStartY = e.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("mouseup", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });

      currentImage.addEventListener("touchstart", function(e) {
        isDragging = true;
        var touch = e.touches[0];
        dragStartX = touch.clientX;
        dragStartY = touch.clientY;
      });

      document.addEventListener("touchmove", function(e) {
        if (isDragging) {
          var touch = e.touches[0];
          var deltaX = touch.clientX - dragStartX;
          var deltaY = touch.clientY - dragStartY;
          dragStartX = touch.clientX;
          dragStartY = touch.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("touchend", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });
    }

    // スライダー自動非表示処理
    var sliderContainer = document.getElementById("zoom-slider-container");
    var imageContainer = document.getElementById("image-container");
    var sliderTimeout;
    function hideSlider() {
      sliderContainer.style.opacity = "0";
    }
    function showSlider() {
      sliderContainer.style.opacity = "0.8";
      clearTimeout(sliderTimeout);
      sliderTimeout = setTimeout(hideSlider, 3000);
    }
    sliderTimeout = setTimeout(hideSlider, 3000);
    imageContainer.addEventListener("mouseover", showSlider);
    imageContainer.addEventListener("mouseleave", function() {
      sliderTimeout = setTimeout(hideSlider, 3000);
    });
  </script>
</body>
</html>
