<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>3.jpg</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    html { scroll-behavior: smooth; }
    a { text-decoration: none; color: #181818; padding: 5px; }
    a:hover { background-color: #d8f6ff; }
    #back-to-top {
      position: fixed; bottom: 30px; right: 30px; width: 50px; height: 50px;
      background-color: rgba(173,216,230,0.7); color: #fff; text-align: center;
      line-height: 50px; border-radius: 50%; font-size: 24px; display: none;
      z-index: 1000; transition: opacity 0.3s; box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    #back-to-top:hover { opacity: 0.9; }
    /* 画像ズーム・ドラッグ用 */
    #current-image {
      transition: transform 0.2s ease;
      backface-visibility: hidden;
      cursor: move;
    }
    /* 矢印ナビゲーション用 */
    .nav-arrow {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      display: none;
      font-size: 2em;
      color: rgba(0, 0, 0, 0.5);
      text-decoration: none;
      user-select: none;
    }
    #image-container:hover .nav-arrow {
      display: block;
    }
    .left-arrow {
      left: 10px;
    }
    .right-arrow {
      right: 10px;
    }
  </style>
</head>
<body style="background-color: #f3f3f3;">
  <div style="text-align:center;">
    
        <div id="image-container" style="position: relative; display: inline-block;">
            <img id="current-image" src="image/3.jpg" alt="3.jpg" style="display:block; width:90%; max-width:90%; margin:0 auto; box-shadow: 0 4px 8px rgba(0,0,0,0.3);">
            <a href="2.html" class="nav-arrow left-arrow">◀</a><a href="4.html" class="nav-arrow right-arrow">▶</a>
            <div id="zoom-slider-container" style="position: absolute; bottom: -30px; left: 50%; transform: translateX(-50%); z-index: 9999; opacity: 0.8; transition: opacity 0.5s;">
                <label style="margin-right: 10px;">－</label>
                <input type="range" id="zoom-slider" min="0.5" max="2" step="0.01" value="1" style="vertical-align: middle;">
                <label style="margin-left: 10px;">＋</label>
            </div>
        </div>
        
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="2.html" rel="prev">&lt; 戻る</a> <a href="4.html" rel="next">進む &gt;</a> <span>3/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
    <div class="text-box" style="background-color: #ffffff; padding: 32px; margin: 50px auto; display:inline-block; text-align:left; max-width:80%; font-size:103%; line-height:1.65;">
      <span style="color: #5a5a5a;">
<strong>スライド内のプレーンテキスト全文</strong><br>
<small>自動抽出のため画像内のテキスト等は表示されない場合があります。</small><br>
<br>
</span>
<!--
**************************************************
****↓↓ テキスト情報 ↓↓***↓↓ここから↓↓****
**************************************************
-->

      概要課題名: 健康相談チャットボットを統合した診療支援ウェブサイトの構築実施する学生: Chau Diem Hoang ( チャウ・ディエム・ホアン)<br>
学生証番号: 102210036 クラス: 21TCLC_Nhat1現在、オンラインで診療予約を行い、医療サービスとチャットボットでやり取りするニーズがますます高まっています。健康相談チャットボットを統合した診療支援ウェブサイトを構築することは、ユーザーのニーズに応えるだけでなく、医療分野におけるデジタルトランスフォーメーションの潮流にも合致しています。このウェブサイトは、ユーザーに診療所、サービス、診療スケジュール、医師に関する詳細かつ信頼性の高い情報を提供します。ユーザーは自分のニーズや予算に合わせたサービスを簡単に探すことができます。さらに、統合されたチャットボットにより、ユーザーは視覚的かつ迅速に検索、予約、健康相談が可能です。透明性のある情報提供により、ユーザーが最適な診療サービスを比較・選択する手助けをします。<br>
「健康相談チャットボットを統合した診療⽀援ウェブサイトの構築」という本テーマは、最新の技術プラットフォームを⽤いて開発されています。バックエンドは Node.js で構築され、⾼いパフォーマンスと拡張性を確保しています。フロントエンドは React、HTML、CSS、JavaScript を活⽤し、ユーザー体験を最適化するためにフレンドリーなインターフェースを設計しています。<br>
特に、チャットボットは ChatGPT をエージェントとして統合し、先進的な⾃<br>
然⾔語処理（NLP）技術を⽤いています。チャットボットはユーザーとインテリジェントに対話し、健康相談や質問に迅速かつ効果的に対応します。<br>
• 序章と第 1 章では、実際のニーズの分析、テーマ実施の動機、目的、プロジェクトの機能、ターゲットユーザーや利用範囲を述べるとともに、<br>
理論的基礎や使用するモデルについて説明します。<br>
• 第 2章では、業務分析、システム設計、データベース設計を行います。<br>
• 第 3 章では、システムの実装、評価、実現された機能について記述します。<br>
• 結論では、達成された成果、限界点、今後の発展方向について述べます。<br>
私は、このウェブサイトがさらに改善されるよう、先生方や友人たちからのご意見をいただけることを大変願っています。
      <br>
<!--
**************************************************
****↑↑ テキスト情報 ↑↑***↑↑ここまで↑↑****
**************************************************
-->

    </div>
    <div style="margin-top: 40px;">
            <a href="1.html">表紙</a> <a href="2.html" rel="prev">&lt; 戻る</a> <a href="4.html" rel="next">進む &gt;</a> <span>3/96</span>
        </div>
        <div style="margin-top: 10px;">
            <a href="contents.html">目次</a> <a href="thumbnail.html">サムネイル表示</a>
        </div>
        
  </div>
  <a id="back-to-top" href="#">↑</a>
  <script>
    // キーボードショートカット
    document.addEventListener('keydown', function(event) {
      if (event.key === 'ArrowLeft') {
        var prevLink = document.querySelector('a[rel="prev"]');
        if (prevLink) { window.location.href = prevLink.href; }
      }
      if (event.key === 'ArrowRight') {
        var nextLink = document.querySelector('a[rel="next"]');
        if (nextLink) { window.location.href = nextLink.href; }
      }
    });
    // 上に戻るボタンの表示
    window.onscroll = function() {
      var backToTop = document.getElementById("back-to-top");
      if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
        backToTop.style.display = "block";
      } else {
        backToTop.style.display = "none";
      }
    };
    document.getElementById("back-to-top").addEventListener("click", function(e) {
      e.preventDefault();
      window.scrollTo({top: 0, behavior: 'smooth'});
    });
    // 画像ズーム・ドラッグ処理
    var slider = document.getElementById("zoom-slider");
    var currentImage = document.getElementById("current-image");

    var currentScale = slider ? parseFloat(slider.value) : 1;
    var currentTranslateX = 0;
    var currentTranslateY = 0;

    function updateTransform() {
      currentImage.style.transform = "translate(" + currentTranslateX + "px, " + currentTranslateY + "px) scale(" + currentScale + ")";
    }

    if (slider && currentImage) {
      slider.addEventListener("input", function() {
        currentScale = parseFloat(this.value);
        updateTransform();
      });
    }

    var isDragging = false;
    var dragStartX = 0;
    var dragStartY = 0;

    if (currentImage) {
      currentImage.addEventListener("mousedown", function(e) {
        isDragging = true;
        dragStartX = e.clientX;
        dragStartY = e.clientY;
        e.preventDefault();
      });

      document.addEventListener("mousemove", function(e) {
        if (isDragging) {
          var deltaX = e.clientX - dragStartX;
          var deltaY = e.clientY - dragStartY;
          dragStartX = e.clientX;
          dragStartY = e.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("mouseup", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });

      currentImage.addEventListener("touchstart", function(e) {
        isDragging = true;
        var touch = e.touches[0];
        dragStartX = touch.clientX;
        dragStartY = touch.clientY;
      });

      document.addEventListener("touchmove", function(e) {
        if (isDragging) {
          var touch = e.touches[0];
          var deltaX = touch.clientX - dragStartX;
          var deltaY = touch.clientY - dragStartY;
          dragStartX = touch.clientX;
          dragStartY = touch.clientY;
          currentTranslateX += deltaX;
          currentTranslateY += deltaY;
          updateTransform();
        }
      });

      document.addEventListener("touchend", function(e) {
        if (isDragging) {
          isDragging = false;
        }
      });
    }

    // スライダー自動非表示処理
    var sliderContainer = document.getElementById("zoom-slider-container");
    var imageContainer = document.getElementById("image-container");
    var sliderTimeout;
    function hideSlider() {
      sliderContainer.style.opacity = "0";
    }
    function showSlider() {
      sliderContainer.style.opacity = "0.8";
      clearTimeout(sliderTimeout);
      sliderTimeout = setTimeout(hideSlider, 3000);
    }
    sliderTimeout = setTimeout(hideSlider, 3000);
    imageContainer.addEventListener("mouseover", showSlider);
    imageContainer.addEventListener("mouseleave", function() {
      sliderTimeout = setTimeout(hideSlider, 3000);
    });
  </script>
</body>
</html>
