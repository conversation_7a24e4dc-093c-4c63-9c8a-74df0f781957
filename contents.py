import os
import re
import sys
from utils.path_utils import calculate_job_paths
from utils.file_utils import get_txt_files_sorted
from utils.template_utils import load_and_render_template

def generate_contents_html(job_id):
    # ジョブパス取得
    paths = calculate_job_paths(job_id)
    txt_dir = paths['txt_dir']
    output_html = os.path.join(paths['html_dir'], "contents.html")

    # ディレクトリ作成
    os.makedirs(txt_dir, exist_ok=True)
    os.makedirs(os.path.dirname(output_html), exist_ok=True)

    # テキストファイル一覧を取得して並び替え（utils使用）
    files = get_txt_files_sorted(txt_dir)

    list_items = []
    for filename in files:
        filepath = os.path.join(txt_dir, filename)
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        # 先頭30文字を抽出
        snippet = content[:35]
        # ファイル名から数字を抽出（例："123.txt" → "123"）
        num_match = re.search(r'\d+', filename)
        num = num_match.group() if num_match else "0"
        # リンクは「番号.html」、表示は「先頭30文字　・・・○○ページ」
        list_item = f'<li style="margin-bottom: 10px;"><a href="{num}.html">{snippet}　・・・{num}ページ</a></li>'
        list_items.append(list_item)

    # ファイル数（画像枚数としてカウント）
    file_count = len(list_items)

    # ナビゲーションバーのHTML
    nav_bar = load_and_render_template(
        'contents_nav_bar.html',
        'components',
        file_count=file_count
    )

    html_content = load_and_render_template(
        'contents_template.html',
        'generators',
        nav_bar=nav_bar,
        list_items="".join(list_items)
    )

    # 生成したHTMLを指定パスに保存
    with open(output_html, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"HTMLファイルを {output_html} に保存しました。")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python contents.py <job_id>")
        sys.exit(1)
    
    job_id = sys.argv[1]
    generate_contents_html(job_id)
