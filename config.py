import os

class Config:
    SECRET_KEY = os.getenv('SECRET_KEY', 'your_secret_key')
    ALLOWED_EXTENSIONS = {'pdf'}
    DEBUG = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'
    
    # 環境変数からパスを取得（.envファイルで管理）
    UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER')
    OUTPUT_BASE_DIR = os.getenv('OUTPUT_BASE_DIR')
    ZIP_HTML_DIR = os.getenv('ZIP_HTML_DIR')
    ZIP_DL_DIR = os.getenv('ZIP_DL_DIR')
    SCRIPTS_DIR = os.getenv('SCRIPTS_DIR', '.')
    
    @property
    def RESULT_ZIP(self):
        return os.path.join(self.ZIP_DL_DIR, 'result.zip')
    
    @staticmethod
    def init_app(app):
        pass

config = Config()
