<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <title>テキスト編集ページ</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="../../../static/css/generators/edit.css">
</head>
<body>
  <h1>テキスト編集ページ</h1>
  <div class="nav-section">
    <a href="1.html" class="nav-link">表紙</a>
    <a href="contents.html" class="nav-link">目次</a>
    <a href="thumbnail.html" class="nav-link">サムネイル一覧</a>
  </div>
  <div class="container">
    {rows_html}
  </div>
  <script>
    // ジョブID設定（グローバル変数）
    var JOB_ID = "{job_id}";
    
    // サーバーURL検出
    var SERVER_URL = "";
    if (window.location.protocol === "file:") {
      // File protocol: use direct server URL
      SERVER_URL = "http://127.0.0.1:5000";
    } else if (window.location.hostname === "localhost" || window.location.hostname === "127.0.0.1") {
      // Served by nginx: use proxy through nginx to avoid CORS
      SERVER_URL = "";
    } else {
      // Direct Flask access: use relative URLs
      SERVER_URL = "";
    }
  </script>
  <script src="../../../static/js/generators/edit.js"></script>
</body>
</html>
