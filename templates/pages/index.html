<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <title>PDF→HTML変換</title>
  <link rel="stylesheet" href="/static/css/common.css">
  <link rel="stylesheet" href="/static/css/pages/index.css">
</head>
<body>
  <header>
    <h1>
      <a href="/">PDF→HTML変換</a>
      <span>(プロトタイプ)</span>
    </h1>
  </header>
  <div id="upload-container">
    <h2>PDF to HTML Converter</h2>
    <p>PDFファイルを美しいインタラクティブHTMLに変換</p>
    <form id="upload-form" method="post" enctype="multipart/form-data">
      <div id="drop-area">
        <p><span class="select-file">PDFファイルを選択</span></p>
        <p>または、ここにファイルをドラッグ&ドロップ</p>
        <input type="file" name="file" id="fileElem" accept="application/pdf" style="display:none;">
      </div>
      <button type="submit" id="convert-btn">変換開始</button>
    </form>
    <div class="progress-overlay" id="progress">
      <div class="spinner"></div>
      <div id="progress-text">アップロード中…</div>
    </div>
  </div>
  <p class="info-text">※このページはサーバー上でのHTML変換を検証するために作成した物で、出力結果を含めデザインは仮の物です。</p>
  <p class="info-text">※現在変換処理の途中で最終結果画面が表示される場合があります。<br>その場合は、数値や画像が正しく反映されるまでF5キーを押して更新願います。（原因調査中）</p>
  <p class="info-text">※MacのサファリブラウザはPDFファイルがアップ出来ない場合があります。</p>
  <button id="restartBtn">Gunicorn を再起動する</button>
  <script>
    document.getElementById("restartBtn").addEventListener("click", function(){
      fetch("/restart", { method: "POST" })
        .then(response => response.json())
        .then(data => {
          if(data.status === "restarted"){
            alert("Gunicorn が再起動されました。");
          } else {
            alert("再起動エラー: " + data.error);
          }
        })
        .catch(error => {
          alert("エラー: " + error);
        });
    });

    var currentJobId = null;

    function uploadFile(file) {
      if (file.type !== 'application/pdf') {
        alert("PDFファイルのみアップロード可能です。PDFファイルをご用意ください。");
        return;
      }
      var formData = new FormData();
      formData.append('file', file);
      var xhr = new XMLHttpRequest();
      xhr.open('POST', '/upload', true);
      xhr.upload.onprogress = function(e) {
        document.getElementById('progress-text').innerHTML = 'アップロード中…';
      };
      xhr.onreadystatechange = function() {
        if(xhr.readyState === 4) {
          if(xhr.status === 200) {
            var response = JSON.parse(xhr.responseText);
            if(response.status === "started" && response.job_id) {
              currentJobId = response.job_id;
              pollStatus();
            } else {
              alert("アップロード中にエラーが発生しました。");
              document.getElementById('progress').style.display = 'none';
            }
          } else {
            alert("アップロード中にエラーが発生しました。");
            document.getElementById('progress').style.display = 'none';
          }
        }
      };
      document.getElementById('progress').style.display = 'flex';
      xhr.send(formData);
    }

    function pollStatus() {
      if (!currentJobId) {
        alert("ジョブIDが見つかりません。");
        document.getElementById('progress').style.display = 'none';
        return;
      }
      
      var interval = setInterval(function(){
        var xhr = new XMLHttpRequest();
        xhr.open('GET', '/status/' + currentJobId, true);
        xhr.onload = function() {
          if(xhr.status === 200) {
            var data = JSON.parse(xhr.responseText);
            document.getElementById('progress-text').innerHTML = '処理中…';
            if(data.status === 'completed') {
              clearInterval(interval);
              window.location.href = "/result/" + currentJobId;
            }
          } else {
            clearInterval(interval);
            alert("ステータス確認中にエラーが発生しました。");
            document.getElementById('progress').style.display = 'none';
          }
        };
        xhr.send();
      }, 1000);
    }

    document.addEventListener('DOMContentLoaded', function(){
      var dropArea = document.getElementById('drop-area');
      var fileElem = document.getElementById('fileElem');
      var convertBtn = document.getElementById('convert-btn');

      dropArea.addEventListener('click', function(){
        fileElem.click();
      });
      fileElem.addEventListener('change', function(){
        if(this.files.length > 0){
          uploadFile(this.files[0]);
        }
      });
      dropArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropArea.classList.add('hover');
      });
      dropArea.addEventListener('dragleave', function(e) {
        dropArea.classList.remove('hover');
      });
      dropArea.addEventListener('drop', function(e) {
        e.preventDefault();
        dropArea.classList.remove('hover');
        var files = e.dataTransfer.files;
        if(files.length > 0){
          if (files[0].type !== 'application/pdf') {
            alert("PDFファイルのみアップロード可能です。PDFファイルをご用意ください。");
            return;
          }
          uploadFile(files[0]);
        }
      });
      convertBtn.addEventListener('click', function(e){
        e.preventDefault();
        if(fileElem.files.length > 0){
          uploadFile(fileElem.files[0]);
        }
      });
    });
  </script>
</body>
</html>