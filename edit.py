#!/usr/bin/env python3
import os
import re
import sys
import html
from utils.path_utils import calculate_job_paths
from utils.file_utils import get_txt_files_sorted
from utils.template_utils import load_and_render_template

def generate_edit_html(job_id):
    # ジョブパス取得
    paths = calculate_job_paths(job_id)

    txt_dir = paths['txt_dir']
    img_dir = paths['image_dir']
    output_file = os.path.join(paths['html_dir'], 'edit.html')

    # ディレクトリ作成
    os.makedirs(txt_dir, exist_ok=True)
    os.makedirs(img_dir, exist_ok=True)

    rows_html = ""

    # テキストファイル（.txt）の一覧を取得（数値順でソート、utils使用）
    txt_files = get_txt_files_sorted(txt_dir)

    for txt_file in txt_files:
        # ファイル名から数字部分を抽出（例："123.txt"→"123"）
        match = re.search(r'(\d+)', txt_file)
        if not match:
            continue
        number = match.group(1)
        
        # テキスト内容を読み込み
        txt_path = os.path.join(txt_dir, txt_file)
        with open(txt_path, 'r', encoding='utf-8') as f:
            text_content = f.read().strip()
        
        # 画像ディレクトリから、テキストファイル名と同じ数字の画像ファイルを検索
        image_file = None
        for f_name in os.listdir(img_dir):
            base, _ = os.path.splitext(f_name)
            if base == number:
                image_file = f_name
                break
        if image_file:
            # HTMLファイルから見た相対パス（edit.htmlはhtml/直下）
            img_path = "image/" + image_file
            # サムネイル画像に影を付与
            img_html = f"<img src='{img_path}' alt='Image'>"
        else:
            img_path = ""
            img_html = "No Image"

        # JS引数を安全にエスケープ
        js_number = number  
        js_img_path = img_path.replace("'", "&#39;").replace('"', "&quot;")  # HTML属性用のクォートをエスケープ
        
        # HTMLテキストコンテンツをエスケープ
        html_text_content = html.escape(text_content)

        # 各行のHTML（1行＝1テキストファイル分）を生成
        row_html = load_and_render_template(
            'edit_row.html',
            'components',
            number=number,
            img_html=img_html,
            js_number=js_number,
            js_img_path=js_img_path,
            html_text_content=html_text_content
        )
        rows_html += row_html

    html_content = load_and_render_template(
        'edit_template.html',
        'generators',
        rows_html=rows_html,
        job_id=job_id
    )

    # 生成したHTMLを指定のファイルに保存
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print("HTMLファイルが生成されました:", output_file)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python edit.py <job_id>")
        sys.exit(1)
    
    job_id = sys.argv[1]
    generate_edit_html(job_id)
